#!/bin/bash

# Script pour tester la détection d'émulateur vs tablette physique
# Usage: ./test_emulator_detection.sh

echo "🔍 TEST: Détection émulateur vs tablette physique"
echo "================================================"

# Vérifier les appareils connectés
echo "📱 Appareils connectés:"
adb devices

# Identifier les appareils
DEVICES=$(adb devices | grep -v "List of devices" | grep "device" | cut -f1)

for DEVICE in $DEVICES; do
    echo ""
    echo "🔍 Testing device: $DEVICE"
    
    # Vérifier les propriétés de l'appareil
    QEMU=$(adb -s $DEVICE shell getprop ro.kernel.qemu 2>/dev/null || echo "")
    MODEL=$(adb -s $DEVICE shell getprop ro.product.model 2>/dev/null || echo "")
    
    echo "  📊 QEMU property: '$QEMU'"
    echo "  📊 Model: '$MODEL'"
    
    # Déterminer si c'est un émulateur
    if [ "$QEMU" = "1" ] || echo "$MODEL" | grep -qi -E "(emulator|sdk|gphone)"; then
        echo "  🎭 RESULT: ÉMULATEUR détecté"
    else
        echo "  📱 RESULT: TABLETTE PHYSIQUE détectée"
    fi
done

echo ""
echo "🚀 Maintenant testez l'application sur chaque appareil pour vérifier"
echo "   que la détection d'émulateur fonctionne correctement !"
