# MediaPipe ProGuard rules
-keep class com.google.mediapipe.** { *; }
-keep class com.google.protobuf.** { *; }
-keep class com.google.common.** { *; }

# Keep annotations
-keepattributes *Annotation*
-keepattributes Signature,InnerClasses

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep classes that are accessed via reflection
-keep class javax.annotation.processing.** { *; }
-keep class javax.lang.model.** { *; }

# Additional rules for AutoValue and related libraries
-keep class com.google.auto.value.** { *; }
-keep class autovalue.shaded.** { *; }

# Keep R8 from stripping necessary classes
-dontwarn com.google.mediapipe.**
-dontwarn com.google.protobuf.**
-dontwarn com.google.common.**
-dontwarn javax.annotation.processing.**
-dontwarn javax.lang.model.**

# Additional rules from missing_rules.txt
-dontwarn javax.annotation.processing.AbstractProcessor
-dontwarn javax.annotation.processing.SupportedAnnotationTypes
-dontwarn javax.lang.model.SourceVersion
-dontwarn javax.lang.model.element.Element
-dontwarn javax.lang.model.element.ElementKind
-dontwarn javax.lang.model.element.Modifier
-dontwarn javax.lang.model.type.TypeMirror
-dontwarn javax.lang.model.type.TypeVisitor
-dontwarn javax.lang.model.util.SimpleTypeVisitor8