#!/usr/bin/env python3

# <PERSON><PERSON> le fichier
with open('MpGestureManager.kt', 'r') as f:
    content = f.read()

# Ancien setResultListener
old_listener = """                .setResultListener { result: GestureRecognizerResult, input: MPImage ->
                    try {
                        android.util.Log.d("MpGestureManager", "🔍 DEBUG: setResultListener appelé")
                        // Traiter les résultats de reconnaissance
                        if (result.gestures().isNotEmpty() && result.handedness().isNotEmpty() && 
                            result.gestures()[0].isNotEmpty() && result.handedness()[0].isNotEmpty()) {
                            val gesture = result.gestures()[0][0]
                            val handedness = result.handedness()[0][0]
                            val gestureName = gesture.categoryName()
                            val confidence = gesture.score().toDouble()
                            val handName = handedness.categoryName()

                            android.util.Log.d("MpGestureManager", "📷 Geste reconnu: $gestureName avec confiance $confidence par la main: $handName")

                            if (confidence >= minScore) {
                                sink?.success(mapOf(
                                    "label" to gestureName,
                                    "score" to confidence,
                                    "hand" to handName,
                                    "ts" to System.currentTimeMillis()
                                ))
                            }
                        } else {
                            android.util.Log.d("MpGestureManager", "🔍 DEBUG: Aucun geste détecté ou listes vides")
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("MpGestureManager", "❌ ERREUR lors du traitement des résultats: ${e.message}")
                        android.util.Log.e("MpGestureManager", "🔍 DEBUG: Stack trace:", e)
                    }
                }"""

# Nouveau setResultListener avec logs améliorés
new_listener = """                .setResultListener { result: GestureRecognizerResult, input: MPImage ->
                    try {
                        android.util.Log.d("MpGestureManager", "🔍 DEBUG: setResultListener appelé")
                        // Traiter les résultats de reconnaissance
                        if (result.gestures().isEmpty()) {
                            android.util.Log.d("MpGestureManager", "Aucun geste détecté sur ce frame")
                            return@setResultListener
                        }

                        if (result.handedness().isEmpty()) {
                            android.util.Log.d("MpGestureManager", "Aucune main détectée sur ce frame")
                            return@setResultListener
                        }

                        if (result.gestures()[0].isEmpty() || result.handedness()[0].isEmpty()) {
                            android.util.Log.d("MpGestureManager", "Listes de gestes ou de mains vides")
                            return@setResultListener
                        }

                        val gesture = result.gestures()[0][0]
                        val handedness = result.handedness()[0][0]
                        val gestureName = gesture.categoryName()
                        val confidence = gesture.score().toDouble()
                        val handName = handedness.categoryName()

                        android.util.Log.d("MpGestureManager", "📷 Geste reconnu: $gestureName avec confiance $confidence par la main: $handName")

                        if (confidence >= minScore) {
                            sink?.success(mapOf(
                                "label" to gestureName,
                                "score" to confidence,
                                "hand" to handName,
                                "ts" to System.currentTimeMillis()
                            ))
                        } else {
                            android.util.Log.d("MpGestureManager", "Confidence trop basse: $confidence < $minScore")
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("MpGestureManager", "❌ ERREUR lors du traitement des résultats: ${e.message}")
                        android.util.Log.e("MpGestureManager", "🔍 DEBUG: Stack trace:", e)
                    }
                }"""

# Remplacer le setResultListener
content = content.replace(old_listener, new_listener)

# Écrire le fichier modifié
with open('MpGestureManager.kt', 'w') as f:
    f.write(content)

print('ResultListener amélioré avec logs détaillés')
