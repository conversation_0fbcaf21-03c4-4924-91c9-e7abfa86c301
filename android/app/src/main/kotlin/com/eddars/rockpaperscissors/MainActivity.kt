package com.eddars.rockpaperscissors

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel
import com.eddars.rockpaperscissors.gesture.MpGestureManager
import android.Manifest
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class MainActivity : FlutterActivity() {

    private var gestureManager: MpGestureManager? = null
    private val CAMERA_PERMISSION_REQUEST_CODE = 1001

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        android.util.Log.d("MainActivity", "🚀 INIT: Configuring Flutter engine...")

        // Initialiser directement le gestionnaire de gestes
        // Les permissions sont gérées par Flutter dans main.dart
        initializeGestureManager()
    }

    private fun initializeGestureManager() {
        // Initialize gesture manager - SEULEMENT MEDIAPIPE
        gestureManager = MpGestureManager(this)
        android.util.Log.d("MainActivity", "✅ INIT: Gesture manager created")

        setupMethodChannel(flutterEngine)
    }

    private fun setupMethodChannel(flutterEngine: FlutterEngine?) {
        if (flutterEngine == null) return
        // Set up method channel for gesture control
        android.util.Log.d("MainActivity", "🔌 CHANNEL: Setting up mp_gesture/methods channel...")
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "mp_gesture/methods").setMethodCallHandler { call, result ->
            when (call.method) {
                "start" -> {
                    val minScore = call.argument<Double>("minScore") ?: 0.65
                    val useFrontCamera = call.argument<Boolean>("useFrontCamera") ?: true
                    gestureManager?.setMinScore(minScore)
                    gestureManager?.setUseFrontCamera(useFrontCamera)
                    gestureManager?.start()
                    result.success(null)
                }
                "stop" -> {
                    gestureManager?.stop()
                    result.success(null)
                }
                else -> result.notImplemented()
            }
        }

        // Set up event channel for gesture results
        android.util.Log.d("MainActivity", "🔌 CHANNEL: Setting up mp_gesture/events channel...")
        EventChannel(flutterEngine.dartExecutor.binaryMessenger, "mp_gesture/events").setStreamHandler(gestureManager)

        android.util.Log.d("MainActivity", "✅ INIT: All channels configured successfully")
    }




}




