#!/usr/bin/env python3

# Lire le fichier
with open('MpGestureManager.kt', 'r') as f:
    content = f.read()

# Supprimer l'import de YuvToRgbConverter
content = content.replace("import androidx.camera.core.YuvToRgbConverter\n", "")

# Supprimer la déclaration de yuvToRgb
content = content.replace(
    "    // Convertisseur YUV vers RGB\n    private val yuvToRgb: YuvToRgbConverter by lazy { YuvToRgbConverter(activity) }\n",
    ""
)

# Ancienne méthode imageProxyToBitmap
old_method = """    // Convertir ImageProxy en Bitmap
    private fun imageProxyToBitmap(image: ImageProxy): Bitmap {
        // 1) convertir YUV_420_888 -> ARGB
        val argb = Bitmap.createBitmap(image.width, image.height, Bitmap.Config.ARGB_8888)
        yuvToRgb.yuvToRgb(image, argb)

        // 2) appliquer la rotation fournie par CameraX (+ miroir si frontale)
        val matrix = Matrix().apply {
            postRotate(image.imageInfo.rotationDegrees.toFloat())
            if (useFrontCamera) {
                postScale(-1f, 1f, argb.width / 2f, argb.height / 2f)
            }
        }
        return Bitmap.createBitmap(argb, 0, 0, argb.width, argb.height, matrix, true)
    }"""

# Nouvelle méthode imageProxyToBitmap avec conversion alternative
new_method = """    // Convertir ImageProxy en Bitmap
    private fun imageProxyToBitmap(image: ImageProxy): Bitmap {
        val yBuffer = image.planes[0].buffer
        val uBuffer = image.planes[1].buffer
        val vBuffer = image.planes[2].buffer

        val ySize = yBuffer.remaining()
        val uSize = uBuffer.remaining()
        val vSize = vBuffer.remaining()

        val nv21 = ByteArray(ySize + uSize + vSize)

        // Copier les données Y
        yBuffer.get(nv21, 0, ySize)

        // Copier les données V et U dans le bon ordre pour NV21
        val vBufferPosition = ySize
        val uBufferPosition = ySize + vSize

        vBuffer.get(nv21, vBufferPosition, vSize)
        uBuffer.get(nv21, uBufferPosition, uSize)

        // Créer un YuvImage et le convertir en JPEG
        val yuvImage = android.graphics.YuvImage(nv21, android.graphics.ImageFormat.NV21, image.width, image.height, null)
        val out = java.io.ByteArrayOutputStream()
        yuvImage.compressToJpeg(android.graphics.Rect(0, 0, image.width, image.height), 100, out)

        // Convertir le JPEG en Bitmap
        val imageBytes = out.toByteArray()
        val bitmap = android.graphics.BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)

        // Appliquer la rotation et le miroir si nécessaire
        val matrix = Matrix().apply {
            postRotate(image.imageInfo.rotationDegrees.toFloat())
            if (useFrontCamera) {
                postScale(-1f, 1f, bitmap.width / 2f, bitmap.height / 2f)
            }
        }

        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }"""

# Remplacer la méthode
content = content.replace(old_method, new_method)

# Écrire le fichier modifié
with open('MpGestureManager.kt', 'w') as f:
    f.write(content)

print('Conversion YUV vers RGB corrigée avec une méthode alternative')
