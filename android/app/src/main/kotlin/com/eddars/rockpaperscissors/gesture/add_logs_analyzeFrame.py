#!/usr/bin/env python3

# <PERSON><PERSON> le fichier
with open('MpGestureManager.kt', 'r') as f:
    content = f.read()

# Ancienne méthode analyzeFrame
old_method = """    private fun analyzeFrame(image: ImageProxy) {
        try {
            // Vérifier si le modèle a été chargé correctement
            if (gestureRecognizer == null) {
                android.util.Log.w("MpGestureManager", "⚠️ MODÈLE: Le modèle n'a pas été chargé, impossibilité d'analyser l'image")
                image.close()
                return
            }

            // Convertir l'image en Bitmap pour MediaPipe
            val bitmap = imageProxyToBitmap(image)

            // Créer une image MediaPipe à partir du bitmap
            val mpImage = BitmapImageBuilder(bitmap).build()

            // Exécuter la reconnaissance de gestes avec MediaPipe en mode asynchrone
            gestureRecognizer?.recognizeAsync(mpImage, System.currentTimeMillis())

        } catch (e: Exception) {
            android.util.Log.e("MpGestureManager", "❌ ERREUR lors de l'analyse de l'image: ${e.message}")
        } finally {
            image.close()
        }"""

# Nouvelle méthode analyzeFrame avec logs
new_method = """    private fun analyzeFrame(image: ImageProxy) {
        android.util.Log.d("MpGestureManager", "🔍 DEBUG: analyzeFrame appelée")
        try {
            // Vérifier si le modèle a été chargé correctement
            if (gestureRecognizer == null) {
                android.util.Log.w("MpGestureManager", "⚠️ MODÈLE: Le modèle n'a pas été chargé, impossibilité d'analyser l'image")
                image.close()
                return
            }

            android.util.Log.d("MpGestureManager", "🔍 DEBUG: Modèle chargé, conversion de l'image en bitmap")

            // Convertir l'image en Bitmap pour MediaPipe
            val bitmap = imageProxyToBitmap(image)
            android.util.Log.d("MpGestureManager", "🔍 DEBUG: Image convertie en bitmap: ${bitmap.width}x${bitmap.height}")

            // Créer une image MediaPipe à partir du bitmap
            val mpImage = BitmapImageBuilder(bitmap).build()
            android.util.Log.d("MpGestureManager", "🔍 DEBUG: Image MediaPipe créée")

            // Exécuter la reconnaissance de gestes avec MediaPipe en mode asynchrone
            gestureRecognizer?.recognizeAsync(mpImage, System.currentTimeMillis())
            android.util.Log.d("MpGestureManager", "🔍 DEBUG: Reconnaissance asynchrone démarrée")

        } catch (e: Exception) {
            android.util.Log.e("MpGestureManager", "❌ ERREUR lors de l'analyse de l'image: ${e.message}")
            android.util.Log.e("MpGestureManager", "🔍 DEBUG: Stack trace:", e)
        } finally {
            image.close()
        }"""

# Remplacer la méthode
content = content.replace(old_method, new_method)

# Écrire le fichier modifié
with open('MpGestureManager.kt', 'w') as f:
    f.write(content)

print('Logs de débogage ajoutés dans la méthode analyzeFrame')
