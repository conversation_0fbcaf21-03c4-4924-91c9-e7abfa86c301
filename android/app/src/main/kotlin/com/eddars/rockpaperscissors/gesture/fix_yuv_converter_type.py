#!/usr/bin/env python3

# <PERSON>re le fichier
with open('MpGestureManager.kt', 'r') as f:
    content = f.read()

# Ancienne déclaration incorrecte
old_declaration = "    private val yuvToRgb by lazy { YuvToRgbConverter(activity) }"

# Nouvelle déclaration avec type explicite
new_declaration = "    private val yuvToRgb: YuvToRgbConverter by lazy { YuvToRgbConverter(activity) }"

# Remplacer la déclaration
content = content.replace(old_declaration, new_declaration)

# Écrire le fichier modifié
with open('MpGestureManager.kt', 'w') as f:
    f.write(content)

print('Type de YuvToRgbConverter corrigé')
