#!/usr/bin/env python3

# <PERSON>re le fichier
with open('MpGestureManager.kt', 'r') as f:
    content = f.read()

# Ajouter l'import pour YuvToRgbConverter
if "import androidx.camera.core.YuvToRgbConverter" not in content:
    content = content.replace(
        "import androidx.camera.core.ImageAnalysis",
        "import androidx.camera.core.ImageAnalysis\nimport androidx.camera.core.YuvToRgbConverter"
    )

# Écrire le fichier modifié
with open('MpGestureManager.kt', 'w') as f:
    f.write(content)

print('Import YuvToRgbConverter ajouté')
