#!/usr/bin/env python3

# Lire le fichier
with open('MpGestureManager.kt', 'r') as f:
    content = f.read()

# Ajouter les imports nécessaires
if "import androidx.concurrent.futures.CallbackToFutureAdapter" not in content:
    content = content.replace(
        "import java.io.OutputStream",
        "import java.io.OutputStream
import androidx.concurrent.futures.CallbackToFutureAdapter
import java.util.concurrent.Executors"
    )

# Ancienne déclaration de variables
old_vars = """    private var analysis: ImageAnalysis? = null
    private var gestureRecognizer: GestureRecognizer? = null
    private var sink: EventChannel.EventSink? = null
    private var minScore: Double = 0.40
    private var useFrontCamera: Boolean = true

    // Variables pour le suivi des frames
    private var frameCount = 0L
    private var lastFpsTs = System.currentTimeMillis()"""

# Nouvelle déclaration de variables avec exécuteur dédié
new_vars = """    private var analysis: ImageAnalysis? = null
    private var gestureRecognizer: GestureRecognizer? = null
    private var sink: EventChannel.EventSink? = null
    private var minScore: Double = 0.40
    private var useFrontCamera: Boolean = true

    // Exécuteur dédié pour l'analyse d'images
    private val cameraExecutor = Executors.newSingleThreadExecutor()

    // Variables pour le suivi des frames
    private var frameCount = 0L
    private var lastFpsTs = System.currentTimeMillis()"""

# Remplacer les variables
content = content.replace(old_vars, new_vars)

# Ancienne configuration de l'analyseur
old_analyzer = """            // Configuration améliorée pour la caméra frontale
            val analyzer = ImageAnalysis.Builder()
                .setTargetResolution(Size(640, 480))
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_YUV_420_888)
                .build()



            analyzer.setAnalyzer(activity.mainExecutor) { imageProxy ->
                android.util.Log.d("MpGestureManager", "📸 INFO: Frame reçu par l'analyseur")
                analyzeFrame(imageProxy)
            }"""

# Nouvelle configuration de l'analyseur
new_analyzer = """            // Configuration améliorée pour la caméra frontale
            val analyzer = ImageAnalysis.Builder()
                .setTargetResolution(Size(640, 480))
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_RGBA_8888)  // Format RGBA pour MediaPipe
                .build()

            android.util.Log.d("MpGestureManager", "🔧 INFO: Configuration de l'analyseur terminée")

            analyzer.setAnalyzer(cameraExecutor) { imageProxy ->
                android.util.Log.d("MpGestureManager", "🧪 ANALYSE: frame reçu")
                try {
                    // Récupérer la rotation de l'image
                    val rotation = imageProxy.imageInfo.rotationDegrees
                    analyzeFrame(imageProxy, rotation)
                } catch (t: Throwable) {
                    android.util.Log.e("MpGestureManager", "❌ Erreur dans l'analyseur", t)
                } finally {
                    imageProxy.close()  // IMPORTANT: toujours fermer l'ImageProxy
                }
            }"""

# Remplacer la configuration de l'analyseur
content = content.replace(old_analyzer, new_analyzer)

# Ancienne méthode analyzeFrame
old_analyze_frame = """    private fun analyzeFrame(image: ImageProxy) {
        try {
            // Vérifier si le modèle a été chargé correctement
            if (gestureRecognizer == null) {
                android.util.Log.w("MpGestureManager", "⚠️ MODÈLE: Le modèle n'a pas été chargé, impossibilité d'analyser l'image")
                image.close()
                return
            }

            // Suivi des frames et calcul du FPS
            frameCount++
            if (frameCount % 15L == 0L) {
                val now = System.currentTimeMillis()
                val dt = now - lastFpsTs
                if (dt > 0) {
                    val fps = 15000.0 / dt
                    android.util.Log.d("MpGestureManager", "Analyzer FPS ~ %.1f".format(fps))
                    lastFpsTs = now
                }
            }

            // Convertir l'image en Bitmap pour MediaPipe
            val bitmap = imageProxyToBitmap(image)
            android.util.Log.d("MpGestureManager", "Bitmap ${bitmap.width}x${bitmap.height}")

            // Créer une image MediaPipe à partir du bitmap
            val mpImage = BitmapImageBuilder(bitmap).build()
            android.util.Log.d("MpGestureManager", "Image MediaPipe créée")

            // Exécuter la reconnaissance de gestes avec MediaPipe en mode asynchrone
            gestureRecognizer?.recognizeAsync(mpImage, System.currentTimeMillis())
            android.util.Log.d("MpGestureManager", "Reconnaissance asynchrone démarrée")

        } catch (e: Exception) {
            android.util.Log.e("MpGestureManager", "❌ ERREUR lors de l'analyse de l'image: ${e.message}")
            android.util.Log.e("MpGestureManager", "🔍 DEBUG: Stack trace:", e)
        } finally {
            image.close()
        }
    }"""

# Nouvelle méthode analyzeFrame avec rotation et sans fermeture d'image (déjà faite dans l'analyseur)
new_analyze_frame = """    private fun analyzeFrame(image: ImageProxy, rotation: Int) {
        try {
            // Vérifier si le modèle a été chargé correctement
            if (gestureRecognizer == null) {
                android.util.Log.w("MpGestureManager", "⚠️ MODÈLE: Le modèle n'a pas été chargé, impossibilité d'analyser l'image")
                return
            }

            // Suivi des frames et calcul du FPS
            frameCount++
            if (frameCount % 15L == 0L) {
                val now = System.currentTimeMillis()
                val dt = now - lastFpsTs
                if (dt > 0) {
                    val fps = 15000.0 / dt
                    android.util.Log.d("MpGestureManager", "Analyzer FPS ~ %.1f".format(fps))
                    lastFpsTs = now
                }
            }

            // Convertir l'image en Bitmap pour MediaPipe
            val bitmap = imageProxyToBitmap(image)
            android.util.Log.d("MpGestureManager", "Bitmap ${bitmap.width}x${bitmap.height}, rotation: $rotation")

            // Créer une image MediaPipe à partir du bitmap
            val mpImage = BitmapImageBuilder(bitmap).build()
            android.util.Log.d("MpGestureManager", "Image MediaPipe créée")

            // Exécuter la reconnaissance de gestes avec MediaPipe en mode asynchrone
            gestureRecognizer?.recognizeAsync(mpImage, System.currentTimeMillis())
            android.util.Log.d("MpGestureManager", "Reconnaissance asynchrone démarrée")

        } catch (e: Exception) {
            android.util.Log.e("MpGestureManager", "❌ ERREUR lors de l'analyse de l'image: ${e.message}")
            android.util.Log.e("MpGestureManager", "🔍 DEBUG: Stack trace:", e)
        }
    }"""

# Remplacer la méthode analyzeFrame
content = content.replace(old_analyze_frame, new_analyze_frame)

# Ajouter une méthode pour arrêter l'exécuteur
if "fun stop() {" in content:
    old_stop = """    fun stop() {
        android.util.Log.d("MpGestureManager", "🛑 STOP: Arrêt de la caméra")
        cameraProviderFuture?.get()?.unbindAll()
        cameraProviderFuture = null
        analysis = null
        android.util.Log.d("MpGestureManager", "✅ STOP: Caméra déliée avec succès")
        android.util.Log.d("MpGestureManager", "✅ STOP: Caméra arrêtée, modèle conservé")
    }"""

    new_stop = """    fun stop() {
        android.util.Log.d("MpGestureManager", "🛑 STOP: Arrêt de la caméra")
        cameraProviderFuture?.get()?.unbindAll()
        cameraProviderFuture = null
        analysis = null
        // Arrêter l'exécuteur
        cameraExecutor.shutdown()
        android.util.Log.d("MpGestureManager", "✅ STOP: Caméra déliée avec succès")
        android.util.Log.d("MpGestureManager", "✅ STOP: Caméra arrêtée, modèle conservé")
    }"""

    content = content.replace(old_stop, new_stop)

# Écrire le fichier modifié
with open('MpGestureManager.kt', 'w') as f:
    f.write(content)

print('Configuration CameraX/ImageAnalysis corrigée avec exécuteur dédié et format RGBA')
