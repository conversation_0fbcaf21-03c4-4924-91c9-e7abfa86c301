#!/usr/bin/env python3

# Lire le fichier
with open('MpGestureManager.kt', 'r') as f:
    content = f.read()

# Ancienne méthode imageProxyToBitmap
old_method = """    // Convertir ImageProxy en Bitmap
    private fun imageProxyToBitmap(image: ImageProxy): Bitmap {
        val yBuffer = image.planes[0].buffer
        val uBuffer = image.planes[1].buffer
        val vBuffer = image.planes[2].buffer

        val ySize = yBuffer.remaining()
        val uSize = uBuffer.remaining()
        val vSize = vBuffer.remaining()

        val nv21 = ByteArray(ySize + uSize + vSize)

        yBuffer.get(nv21, 0, ySize)
        vBuffer.get(nv21, ySize, vSize)
        uBuffer.get(nv21, ySize + vSize, uSize)

        val yuvImage = android.graphics.YuvImage(nv21, android.graphics.ImageFormat.NV21, image.width, image.height, null)
        val out = java.io.ByteArrayOutputStream()
        yuvImage.compressToJpeg(android.graphics.Rect(0, 0, image.width, image.height), 100, out)

        val imageBytes = out.toByteArray()
        val bitmap = android.graphics.BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)

        // Rotation du bitmap si nécessaire
        val matrix = Matrix()
        matrix.postRotate(image.imageInfo.rotationDegrees.toFloat())
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }"""

# Nouvelle méthode imageProxyToBitmap avec conversion correcte
new_method = """    // Convertir ImageProxy en Bitmap
    private fun imageProxyToBitmap(image: ImageProxy): Bitmap {
        // 1) convertir YUV_420_888 -> ARGB
        val argb = Bitmap.createBitmap(image.width, image.height, Bitmap.Config.ARGB_8888)
        yuvToRgb.yuvToRgb(image, argb)

        // 2) appliquer la rotation fournie par CameraX (+ miroir si frontale)
        val matrix = Matrix().apply {
            postRotate(image.imageInfo.rotationDegrees.toFloat())
            if (useFrontCamera) {
                postScale(-1f, 1f, argb.width / 2f, argb.height / 2f)
            }
        }
        return Bitmap.createBitmap(argb, 0, 0, argb.width, argb.height, matrix, true)
    }"""

# Remplacer la méthode
content = content.replace(old_method, new_method)

# Écrire le fichier modifié
with open('MpGestureManager.kt', 'w') as f:
    f.write(content)

print('Méthode imageProxyToBitmap corrigée avec YuvToRgbConverter')
