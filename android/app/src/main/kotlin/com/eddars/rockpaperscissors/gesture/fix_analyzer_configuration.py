#!/usr/bin/env python3

# <PERSON><PERSON> le fichier
with open('MpGestureManager.kt', 'r') as f:
    content = f.read()

# Ancienne configuration de l'analyseur
old_config = """            analyzer.setAnalyzer(activity.mainExecutor) { imageProxy ->
                analyzeFrame(imageProxy)
            }"""

# Nouvelle configuration de l'analyseur avec logs et ContextCompat.getMainExecutor
new_config = """            // Utiliser un exécuteur plus robuste
            val executor = ContextCompat.getMainExecutor(activity)
            android.util.Log.d("MpGestureManager", "🔧 INFO: Exécuteur configuré pour l'analyseur")

            analyzer.setAnalyzer(executor) { imageProxy ->
                android.util.Log.d("MpGestureManager", "📸 INFO: Frame reçu par l'analyseur")
                analyzeFrame(imageProxy)
            }"""

# Remplacer la configuration
content = content.replace(old_config, new_config)

# Écrire le fichier modifié
with open('MpGestureManager.kt', 'w') as f:
    f.write(content)

print('Configuration de l'analyseur corrigée avec logs et exécuteur robuste')
