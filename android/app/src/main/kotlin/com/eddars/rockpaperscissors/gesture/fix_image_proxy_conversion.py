#!/usr/bin/env python3

# Lire le fichier
with open('MpGestureManager.kt', 'r') as f:
    content = f.read()

# Ajouter les imports nécessaires
if "import androidx.camera.core.ImageProxy" not in content:
    content = content.replace(
        "import androidx.camera.core.ImageAnalysis",
        "import androidx.camera.core.ImageAnalysis\nimport androidx.camera.core.ImageProxy\nimport androidx.camera.core.YuvToRgbConverter"
    )

# Ajouter le champ de classe pour YuvToRgbConverter
if "private val yuvToRgb by lazy { YuvToRgbConverter(activity) }" not in content:
    content = content.replace(
        "    private var useFrontCamera: Boolean = true",
        "    private var useFrontCamera: Boolean = true\n\n    // Convertisseur YUV vers RGB\n    private val yuvToRgb by lazy { YuvToRgbConverter(activity) }"
    )

# Ancienne méthode imageProxyToBitmap
old_method = """    private fun imageProxyToBitmap(image: ImageProxy): Bitmap {
        val yBuffer = image.planes[0].buffer
        val uBuffer = image.planes[1].buffer
        val vBuffer = image.planes[2].buffer

        val ySize = yBuffer.remaining()
        val uSize = uBuffer.remaining()
        val vSize = vBuffer.remaining()

        val nv21 = ByteArray(ySize + uSize + vSize)

        yBuffer.get(nv21, 0, ySize)
        vBuffer.get(nv21, ySize, vSize)
        uBuffer.get(nv21, ySize + vSize, uSize)

        val yuvImage = android.graphics.YuvImage(nv21, android.graphics.ImageFormat.NV21, image.width, image.height, null)
        val out = java.io.ByteArrayOutputStream()
        yuvImage.compressToJpeg(android.graphics.Rect(0, 0, image.width, image.height), 100, out)

        val imageBytes = out.toByteArray()
        val bitmap = android.graphics.BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)

        // Rotation du bitmap si nécessaire
        val matrix = Matrix()
        matrix.postRotate(image.imageInfo.rotationDegrees.toFloat())
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }"""

# Nouvelle méthode imageProxyToBitmap avec conversion correcte
new_method = """    private fun imageProxyToBitmap(image: ImageProxy): Bitmap {
        // 1) convertir YUV_420_888 -> ARGB
        val argb = Bitmap.createBitmap(image.width, image.height, Bitmap.Config.ARGB_8888)
        yuvToRgb.yuvToRgb(image, argb)

        // 2) appliquer la rotation fournie par CameraX (+ miroir si frontale)
        val matrix = Matrix().apply {
            postRotate(image.imageInfo.rotationDegrees.toFloat())
            if (useFrontCamera) {
                postScale(-1f, 1f, argb.width / 2f, argb.height / 2f)
            }
        }
        return Bitmap.createBitmap(argb, 0, 0, argb.width, argb.height, matrix, true)
    }"""

# Remplacer la méthode
content = content.replace(old_method, new_method)

# Ajouter les variables pour le suivi des frames
if "private var frameCount = 0L" not in content:
    content = content.replace(
        "    private var useFrontCamera: Boolean = true",
        "    private var useFrontCamera: Boolean = true\n    \n    // Variables pour le suivi des frames\n    private var frameCount = 0L\n    private var lastFpsTs = System.currentTimeMillis()"
    )

# Écrire le fichier modifié
with open('MpGestureManager.kt', 'w') as f:
    f.write(content)

print('Conversion imageProxyToBitmap corrigée et imports ajoutés')
