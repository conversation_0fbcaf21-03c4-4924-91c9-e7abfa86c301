#!/usr/bin/env python3

# <PERSON><PERSON> le fichier
with open('MpGestureManager.kt', 'r') as f:
    content = f.read()

# Ancienne méthode analyzeFrame
old_method = """    private fun analyzeFrame(image: ImageProxy) {
        try {
            // Vérifier si le modèle a été chargé correctement
            if (gestureRecognizer == null) {
                android.util.Log.w("MpGestureManager", "⚠️ MODÈLE: Le modèle n'a pas été chargé, impossibilité d'analyser l'image")
                image.close()
                return
            }

            // Convertir l'image en Bitmap pour MediaPipe
            val bitmap = imageProxyToBitmap(image)

            // Créer une image MediaPipe à partir du bitmap
            val mpImage = BitmapImageBuilder(bitmap).build()

            // Exécuter la reconnaissance de gestes avec MediaPipe en mode asynchrone
            gestureRecognizer?.recognizeAsync(mpImage, System.currentTimeMillis())

        } catch (e: Exception) {
            android.util.Log.e("MpGestureManager", "❌ ERREUR lors de l'analyse de l'image: ${e.message}")
        } finally {
            image.close()
        }
    }"""

# Nouvelle méthode analyzeFrame avec logs et suivi des frames
new_method = """    private fun analyzeFrame(image: ImageProxy) {
        try {
            // Vérifier si le modèle a été chargé correctement
            if (gestureRecognizer == null) {
                android.util.Log.w("MpGestureManager", "⚠️ MODÈLE: Le modèle n'a pas été chargé, impossibilité d'analyser l'image")
                image.close()
                return
            }

            // Suivi des frames et calcul du FPS
            frameCount++
            if (frameCount % 15L == 0L) {
                val now = System.currentTimeMillis()
                val dt = now - lastFpsTs
                if (dt > 0) {
                    val fps = 15000.0 / dt
                    android.util.Log.d("MpGestureManager", "Analyzer FPS ~ %.1f".format(fps))
                    lastFpsTs = now
                }
            }

            // Convertir l'image en Bitmap pour MediaPipe
            val bitmap = imageProxyToBitmap(image)
            android.util.Log.d("MpGestureManager", "Bitmap ${bitmap.width}x${bitmap.height}")

            // Créer une image MediaPipe à partir du bitmap
            val mpImage = BitmapImageBuilder(bitmap).build()
            android.util.Log.d("MpGestureManager", "Image MediaPipe créée")

            // Exécuter la reconnaissance de gestes avec MediaPipe en mode asynchrone
            gestureRecognizer?.recognizeAsync(mpImage, System.currentTimeMillis())
            android.util.Log.d("MpGestureManager", "Reconnaissance asynchrone démarrée")

        } catch (e: Exception) {
            android.util.Log.e("MpGestureManager", "❌ ERREUR lors de l'analyse de l'image: ${e.message}")
            android.util.Log.e("MpGestureManager", "🔍 DEBUG: Stack trace:", e)
        } finally {
            image.close()
        }
    }"""

# Remplacer la méthode
content = content.replace(old_method, new_method)

# Écrire le fichier modifié
with open('MpGestureManager.kt', 'w') as f:
    f.write(content)

print('Méthode analyzeFrame améliorée avec logs et suivi des frames')
