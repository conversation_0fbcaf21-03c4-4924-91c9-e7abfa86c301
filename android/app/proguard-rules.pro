# Règles ProGuard pour MediaPipe et Rock Paper Scissors

# Garder toutes les classes MediaPipe
-keep class com.google.mediapipe.** { *; }
-dontwarn com.google.mediapipe.**

# Garder les classes Protobuf (essentiel pour MediaPipe)
-keep class com.google.protobuf.** { *; }
-keepclassmembers class com.google.protobuf.** { *; }
-dontwarn com.google.protobuf.**

# Garder les classes de gestion des gestes
-keep class com.eddars.rockpaperscissors.gesture.** { *; }

# Garder les classes Flutter
-keep class io.flutter.** { *; }
-dontwarn io.flutter.**

# Garder les classes Camera
-keep class androidx.camera.** { *; }
-dontwarn androidx.camera.**

# Garder les classes Guava utilisées par MediaPipe
-keep class com.google.common.** { *; }
-dontwarn com.google.common.**

# Règles spécifiques pour résoudre les erreurs R8 avec MediaPipe
-dontwarn javax.annotation.processing.**
-dontwarn javax.lang.model.**
-dontwarn com.google.auto.value.**
-dontwarn autovalue.shaded.**

# Garder les classes AutoValue et annotation processing
-keep class com.google.auto.value.** { *; }
-keep class autovalue.shaded.** { *; }

# Garder les annotations
-keepattributes *Annotation*

# Garder les classes natives
-keepclasseswithmembernames class * {
    native <methods>;
}

# Optimisations générales
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Règles supplémentaires pour éviter les problèmes avec les champs Protobuf
-keep @androidx.annotation.Keep class * { *; }
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}

