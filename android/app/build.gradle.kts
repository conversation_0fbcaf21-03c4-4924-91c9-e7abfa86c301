// android/app/build.gradle.kts  — multi‑ABI for debug, arm64‑only for release
plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.eddars.rockpaperscissors"
    compileSdk = 36
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions { jvmTarget = "11" }

    defaultConfig {
        applicationId = "com.eddars.rockpaperscissors"
        minSdk = 24
        targetSdk = 36
        versionCode = 1
        versionName = "1.0"
    }

    buildTypes {
        debug {
            ndk {
                // Include ALL architectures for maximum compatibility
                abiFilters += listOf("arm64-v8a", "armeabi-v7a", "x86_64", "x86")
            }
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = true
            // Force inclusion of native libraries for debug builds
            packagingOptions {
                jniLibs {
                    useLegacyPackaging = true
                }
            }
        }
        release {
            // keep APK small for release on physical devices
            ndk {
                abiFilters.clear()
                abiFilters += listOf("arm64-v8a", "armeabi-v7a")
            }
            signingConfig = signingConfigs.getByName("debug")
            // Réactiver l'optimisation maintenant que la détection fonctionne
            isMinifyEnabled = true  // Réactiver la minification
            isShrinkResources = true // Réactiver la réduction des ressources
            isDebuggable = false // Désactiver le debug pour la production
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }

    packaging {
        jniLibs {
            useLegacyPackaging = true
            // Forcer l'inclusion des bibliothèques MediaPipe pour toutes les architectures
            pickFirsts += listOf(
                "**/libmediapipe_tasks_vision_jni.so",
                "**/libmediapipe_tasks_core_jni.so",
                "**/libmediapipe_jni.so"
            )
            // Exclure les architectures non supportées par MediaPipe si nécessaire
            excludes += listOf(
                "**/libmediapipe_tasks_vision_jni_unsupported.so"
            )
        }
        resources {
            pickFirsts += listOf(
                "META-INF/LICENSE.md",
                "META-INF/LICENSE-notice.md",
                "META-INF/DEPENDENCIES"
            )
        }
    }


}

flutter { source = "../.." }

dependencies {
    // Dépendances de base pour la caméra
    implementation("androidx.camera:camera-core:1.3.4")
    implementation("androidx.camera:camera-camera2:1.3.4")
    implementation("androidx.camera:camera-lifecycle:1.3.4")
    implementation("androidx.camera:camera-view:1.3.4")

    // Dépendances MediaPipe pour la reconnaissance de gestes - version compatible avec Protobuf
    implementation("com.google.mediapipe:tasks-vision:0.10.5") {
        // Exclure les conflits potentiels
        exclude(group = "com.google.protobuf", module = "protobuf-java")
    }
    implementation("com.google.mediapipe:tasks-core:0.10.5") {
        exclude(group = "com.google.protobuf", module = "protobuf-java")
    }

    // Version spécifique de Protobuf qui fonctionne avec MediaPipe
    implementation("com.google.protobuf:protobuf-javalite:3.19.4")
}


