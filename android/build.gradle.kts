buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        // Aligne avec ton gradle-wrapper 8.12 (comme dans ton backup)
        classpath("com.android.tools.build:gradle:8.7.3")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:2.0.0")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

// Écrit les sorties sous <projet>/build/... (Flutter aime bien ce layout)
rootProject.layout.buildDirectory.set(file("../build"))
subprojects {
    project.layout.buildDirectory.set(file("${rootProject.layout.buildDirectory.get()}/${project.name}"))
    project.evaluationDependsOn(":app")
}







tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}


