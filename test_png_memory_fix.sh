#!/bin/bash

# Test script to verify PNG image memory fixes during long gameplay sessions
# This script helps monitor memory usage and image cache behavior

echo "🎮 Rock Paper Scissors - PNG Image Memory Test"
echo "=============================================="
echo ""

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter command not found. Please install Flutter first."
    exit 1
fi

echo "📱 Starting Flutter app in debug mode..."
echo "This will help monitor memory usage during gameplay."
echo ""

# Start the app in debug mode with verbose logging
echo "📊 To monitor the fixes during gameplay, look for these log messages:"
echo "   🖼️ IMAGE_CACHE: Initialized with X images, XMB limit"
echo "   📊 IMAGE_CACHE: Periodic check - X images (X%), X% memory"
echo "   🧹 IMAGE_CACHE: Cache getting full, performing cleanup..."
echo "   🗑️ IMAGE_CACHE: Evicted [image_path]"
echo "   🔄 OPTIMIZED_IMAGE: Attempting retry X for [image_path]"
echo "   🧹 MEMORY: Cleared game images from cache"
echo ""

echo "🎯 Test Instructions:"
echo "1. Start a game and play multiple rounds (at least 20-30 rounds)"
echo "2. Keep the game running for 10+ minutes"
echo "3. Watch for cache maintenance messages every 2 minutes"
echo "4. Verify that PNG images continue to display correctly"
echo "5. Check that memory usage remains stable"
echo ""

echo "⚠️  If you notice any PNG images not displaying:"
echo "   - Check the console for image error messages"
echo "   - Look for cache cleanup messages"
echo "   - Verify fallback widgets are shown if images fail"
echo ""

# Function to monitor memory if available
monitor_memory() {
    if command -v top &> /dev/null; then
        echo "📈 Memory monitoring available. Use 'top -pid \$PID' in another terminal."
    fi
}

echo "🚀 Starting Flutter app..."
flutter run --debug --verbose

echo ""
echo "✅ Test completed. Review the logs above for any issues."
echo "📋 Expected behaviors during long gameplay:"
echo "   ✓ Images load correctly throughout the session"
echo "   ✓ Cache maintenance runs every 2 minutes"
echo "   ✓ Memory usage remains stable"
echo "   ✓ Fallback widgets appear if images fail to load"
echo "   ✓ No 'Image not found' or memory crash errors"