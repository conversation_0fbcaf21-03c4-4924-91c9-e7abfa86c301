# 🔧 Diagnostic : Détection de gestes ne fonctionne pas sur tablette physique

## Problème identifié
L'application fonctionne parfaitement sur l'émulateur mais la détection de gestes ne s'effectue pas sur une tablette physique.

## Solutions appliquées

### ✅ 1. Gestion des permissions runtime
**Problème** : Les permissions caméra ne sont pas demandées explicitement à l'exécution.
**Solution** : Ajout de la demande de permission caméra dans `MainActivity.kt`

### ✅ 2. Configuration MediaPipe optimisée
**Problème** : Seuils de détection trop bas pour les appareils physiques.
**Solution** : Augmentation des seuils de confiance (0.1f → 0.3f)

### ✅ 3. Désactivation temporaire de la minification
**Problème** : ProGuard/R8 peut obfusquer les classes MediaPipe en mode release.
**Solution** : Désactivation temporaire de `isMinifyEnabled` et `isShrinkResources`

### ✅ 4. Amélioration des logs de diagnostic
**Problème** : Manque de visibilité sur les erreurs spécifiques aux appareils physiques.
**Solution** : Ajout de logs détaillés avec informations sur l'appareil

## 🚀 Instructions de test

### Étape 1 : Rebuild et test
```bash
# Rendre le script exécutable
chmod +x test_gesture_debug.sh

# Lancer le test complet
./test_gesture_debug.sh
```

### Étape 2 : Vérifier les logs
Recherchez dans les logs :
- `📷 PERMISSION: Requesting camera permission...` - Permission demandée
- `✅ PERMISSION: Camera permission granted` - Permission accordée
- `🎯 RESULT: Résultat de reconnaissance reçu - Device: [MODEL]` - Détection active
- `📷 ENVOI: Envoi du résultat à Flutter` - Gestes détectés

### Étape 3 : Tests spécifiques sur tablette

1. **Test des permissions** :
   - Vérifiez que l'app demande la permission caméra au premier lancement
   - Accordez la permission et redémarrez l'app

2. **Test de la caméra** :
   - Vérifiez que la caméra s'active (LED caméra allumée)
   - Testez avec caméra avant et arrière

3. **Test des gestes** :
   - Placez votre main bien visible devant la caméra
   - Testez les 3 gestes : pierre, papier, ciseaux
   - Maintenez chaque geste 2-3 secondes

## 🔍 Diagnostic avancé

Si le problème persiste, vérifiez :

### Architecture de l'appareil
```bash
adb shell getprop ro.product.cpu.abi
```
Doit retourner `arm64-v8a` ou `armeabi-v7a`

### Permissions système
```bash
adb shell pm list permissions | grep CAMERA
adb shell dumpsys package com.eddars.rockpaperscissors | grep permission
```

### Logs MediaPipe spécifiques
```bash
adb logcat | grep -E "(MediaPipe|tasks-vision)" --color=always
```

## 🛠️ Solutions alternatives

### Si les permissions ne fonctionnent pas :
Ajoutez dans `AndroidManifest.xml` :
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission-sdk-23 android:name="android.permission.CAMERA" />
```

### Si MediaPipe ne se charge pas :
Vérifiez que les fichiers `.tflite` sont bien inclus dans l'APK :
```bash
unzip -l app-debug.apk | grep -E "\.(tflite|task)$"
```

### Si les gestes ne sont pas reconnus :
Réduisez temporairement le `minScore` dans le code :
```kotlin
val minScore = call.argument<Double>("minScore") ?: 0.4  // Au lieu de 0.65
```

## 📋 Checklist de vérification

- [ ] Permission caméra accordée
- [ ] Caméra s'active (LED allumée)
- [ ] Logs MediaPipe présents
- [ ] Architecture ARM compatible
- [ ] APK en mode debug (temporairement)
- [ ] Gestes maintenus 2-3 secondes
- [ ] Éclairage suffisant
- [ ] Main bien visible dans le cadre

## 🎯 Résultats attendus

Après ces modifications, vous devriez voir dans les logs :
```
🎯 RESULT: Résultat de reconnaissance reçu - Device: [VOTRE_TABLETTE]
📷 ENVOI: Envoi du résultat à Flutter - Device: [VOTRE_TABLETTE], Gesture: Closed_Fist, Score: 0.85
✅ SUCCESS: Résultat envoyé avec succès à Flutter
```

Et dans l'application, les gestes devraient être détectés et affichés correctement.
