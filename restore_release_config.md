# 🔄 Restauration de la configuration Release

## Une fois le problème résolu

Quand la détection de gestes fonctionne correctement sur la tablette physique, vous pouvez réactiver l'optimisation pour réduire la taille de l'APK.

### Étape 1 : Réactiver la minification

Dans `android/app/build.gradle.kts`, remplacez :

```kotlin
release {
    // keep APK small for release on physical devices
    ndk {
        abiFilters.clear()
        abiFilters += listOf("arm64-v8a", "armeabi-v7a")
    }
    signingConfig = signingConfigs.getByName("debug")
    // TEMPORAIREMENT désactivé pour diagnostiquer le problème sur tablette physique
    isMinifyEnabled = false  // Désactiver la minification temporairement
    isShrinkResources = false // Désactiver la réduction des ressources temporairement
    isDebuggable = true // Activer le debug pour les logs
    proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
}
```

Par :

```kotlin
release {
    // keep APK small for release on physical devices
    ndk {
        abiFilters.clear()
        abiFilters += listOf("arm64-v8a", "armeabi-v7a")
    }
    signingConfig = signingConfigs.getByName("debug")
    isMinifyEnabled = true  // Réactiver la minification
    isShrinkResources = true // Réactiver la réduction des ressources
    isDebuggable = false // Désactiver le debug pour la production
    proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
}
```

### Étape 2 : Tester la version optimisée

```bash
flutter build apk --release
flutter install --release
```

### Étape 3 : Vérifier que tout fonctionne

Si la détection de gestes ne fonctionne plus après réactivation de la minification, le problème vient des règles ProGuard. Dans ce cas, ajoutez dans `proguard-rules.pro` :

```proguard
# Règles supplémentaires si nécessaire
-keep class com.google.mediapipe.tasks.** { *; }
-keep class com.google.mediapipe.framework.** { *; }
-dontwarn com.google.mediapipe.tasks.**
-dontwarn com.google.mediapipe.framework.**
```
