#!/bin/bash

# Script pour capturer les logs de la tablette physique
# Usage: ./debug_tablette.sh

echo "🔍 DIAGNOSTIC: Test final de détection de gestes"
echo "==============================================="

# Vérifier les appareils connectés
echo "📱 Vérification des appareils connectés..."
adb devices

# Identifier la tablette
TABLET_ID=$(adb devices | grep -v "List of devices" | grep "device" | head -1 | cut -f1)

if [ -z "$TABLET_ID" ]; then
    echo "❌ Aucune tablette détectée. Veuillez connecter votre tablette."
    exit 1
fi

echo "📱 Tablette détectée: $TABLET_ID"
echo ""

# Installer l'APK sur la tablette
echo "📲 Installation de l'APK sur la tablette..."
adb -s $TABLET_ID install -r build/app/outputs/flutter-apk/app-debug.apk

echo ""
echo "🚀 INSTRUCTIONS:"
echo "1. Ouvrez l'application RockPaperScissors sur votre tablette"
echo "2. Allez dans 'Gesture Training'"
echo "3. Cliquez sur 'Start Training'"
echo "4. Placez votre main devant la caméra frontale"
echo "5. Faites des gestes: pierre (poing), papier (main ouverte), ciseaux (V)"
echo ""
echo "🔍 Ce que nous cherchons dans les logs:"
echo "- ⚠️ Buffer size error... (MediaImage échoue - normal)"
echo "- ✅ Using Bitmap fallback... (Bitmap fonctionne - bon signe)"
echo "- 🎯 RESULT: Résultat de reconnaissance... (MediaPipe traite - excellent)"
echo "- 👋 MAINS: X main(s) détectée(s) (Détection de main - parfait)"
echo "- ✋ GESTES: X geste(s) détecté(s) (Détection de geste - victoire!)"
echo ""
echo "📋 Logs en temps réel (Ctrl+C pour arrêter):"
echo "=============================================="

# Nettoyer les logs et capturer ceux de la tablette
adb -s $TABLET_ID logcat -c
adb -s $TABLET_ID logcat | grep -E "(MpGestureManager|buffer|fallback|Bitmap|RESULT|MAINS|GESTES|🎯|📷|✅|❌)" --color=always
