#!/bin/bash

# Script de test et debug pour la détection de gestes sur tablette physique
# Usage: ./test_gesture_debug.sh

echo "🔍 DIAGNOSTIC: Test de détection de gestes sur tablette physique"
echo "=================================================="

# Vérifier si un appareil est connecté
echo "📱 Vérification des appareils connectés..."
adb devices -l

echo ""
echo "🚀 L'APK a déjà été buildé et installé avec succès !"
echo "📱 Ouvrez maintenant l'application RockPaperScissors sur votre tablette"
echo "🎮 Allez dans le mode jeu et testez la détection de gestes"
echo ""
echo "📋 Logs en temps réel (appuyez sur Ctrl+C pour arrêter) :"
echo "=================================================="

# Nettoyer les logs existants et capturer les nouveaux
adb logcat -c  # Clear existing logs
adb logcat | grep -E "(MpGestureManager|MainActivity|CAMERA|GESTURE|PERMISSION|🎯|📷|✅|❌|🔴)" --color=always
