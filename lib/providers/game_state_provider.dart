import 'dart:async';
import  'package:flutter/foundation.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:audioplayers/audioplayers.dart';
import '../services/audio_manager.dart';


// Helper pour sérialiser les opérations asynchrones
class AsyncMutex {
  Future<void> _last = Future.value();
  Future<T> run<T>(FutureOr<T> Function() action) {
    final c = Completer<T>();
    _last = _last.whenComplete(() async {
      try {
        final res = await action();
        c.complete(res);
      } catch (e, st) {
        c.completeError(e, st);
      }
    });
    return c.future;
  }
}

class GameStateProvider extends ChangeNotifier {

// Mutex asynchrone simple pour sérialiser les mises à jour d'état
final AsyncMutex _mutex = AsyncMutex();
bool _notifying = false;

Future<T> runLocked<T>(FutureOr<T> Function() action) {
  return _mutex.run<T>(action);
}

void safeNotify() {
  if (_notifying) return;
  _notifying = true;
  scheduleMicrotask(() {
    _notifying = false;
    // ignore: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member
    notifyListeners();
  });
}

  // Game state variables
  String _currentPlayer = "";
  bool _playerSelected = false;
  int _initialLives = 3;
  int _playerLives = 3;
  int _programLives = 3;
  int _scorePlayer = 0;
  int _scoreProgram = 0;
  int _playerJackpotPoints = 0;
  int _programJackpotPoints = 0;
  int _consecutiveWinsPlayer = 0;
  int _consecutiveWinsProgram = 0;
  int _minutes = 0;
  int _seconds = 0;
  
  // Game settings
  String _difficulty = "medium";
  String _bgImage = "assets/bg_img/bg_img_3.webp";
  String? _bgMusic = "sunrise1.mp3"; // Default background music
  String _sineshifterSound = "watt.mp3";
  int _volumeLevel = 50;
  int _countdownVolumeLevel = 75;
  
  // Audio players (legacy - will be replaced by AudioManager)
  final AudioPlayer _backgroundMusicPlayer = AudioPlayer();
  final AudioPlayer _effectsPlayer = AudioPlayer();

  // New audio manager
  final AudioManager _audioManager = AudioManager();
  
  // Timer
  Timer? _gameTimer;
  
  // Players data
  Map<String, dynamic> _playersData = {"players": {}};

  // Constructor to initialize audio volume
  GameStateProvider() {
    // Initialize audio in the background to avoid blocking the constructor
    _initializeAudio();
    // Load saved settings on startup
    _loadSettingsOnStartup();
  }

  // Load settings from storage at startup
  // Load settings from storage at startup
Future<void> _loadSettingsOnStartup() async {
  try {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/settings.json');
    if (await file.exists()) {
      final contents = await file.readAsString();
      final Map<String, dynamic> settings = json.decode(contents);

      _volumeLevel = (settings["volume"] ?? 50);
      _countdownVolumeLevel = (settings["countdown_volume"] ?? 75);
      _bgMusic = settings["bg_music"];
      _difficulty = (settings["difficulty"] ?? "medium");
      _initialLives = (settings["lives"] ?? 3);
      _playerLives = _initialLives;
      _programLives = _initialLives;
      _sineshifterSound = (settings["sineshifter_sound"] ?? "watt.mp3");

      if (settings["bg_image"] != null) {
        _bgImage = 'assets/bg_img/${settings["bg_image"]}';
      }

      debugPrint('✅ STARTUP: Settings loaded - Music: $_bgMusic, Volume: $_volumeLevel%, Lives: $_initialLives');
      safeNotify();

      if (_bgMusic != null) {
        Future.delayed(const Duration(milliseconds: 1000), _playBackgroundMusic);
      }
    } else {
      debugPrint('ℹ️ STARTUP: No settings file found, using defaults.');
    }
  } catch (e, st) {
    debugPrint('⚠️ _loadSettingsOnStartup error: $e\n$st');
  }
}

  Future<void> _initializeAudio() async {
    // Initialize the new audio manager
    await _audioManager.initialize();

    // Set volume from settings
    await _audioManager.setGlobalVolume(_volumeLevel / 100.0);

    // Set countdown volume for critical effects
    await _audioManager.setCategoryVolume(AudioType.criticalEffect, _countdownVolumeLevel / 100.0);

    // Legacy audio players (for backward compatibility during transition)
    await _setVolume(); // Set initial volume for both players

    // Configurer les modes de release pour les lecteurs
    await _backgroundMusicPlayer.setReleaseMode(ReleaseMode.loop);
    await _effectsPlayer.setReleaseMode(ReleaseMode.release);

    debugPrint('🔊 AUDIO: Audio players initialized (legacy + new AudioManager)');
    debugPrint('🔊 AUDIO: Background music volume: $_volumeLevel%, Countdown volume: $_countdownVolumeLevel%');
  }

  // Getters
  String get currentPlayer => _currentPlayer;
  bool get playerSelected => _playerSelected;
  int get initialLives => _initialLives;
  int get playerLives => _playerLives;
  int get programLives => _programLives;
  int get scorePlayer => _scorePlayer;
  int get scoreProgram => _scoreProgram;
  int get playerJackpotPoints => _playerJackpotPoints;
  int get programJackpotPoints => _programJackpotPoints;
  int get consecutiveWinsPlayer => _consecutiveWinsPlayer;
  int get consecutiveWinsProgram => _consecutiveWinsProgram;
  int get minutes => _minutes;
  int get seconds => _seconds;
  String get difficulty => _difficulty;
  String get bgImage => _bgImage;
  String? get bgMusic => _bgMusic;
  String get sineshifterSound => _sineshifterSound;
  int get volumeLevel => _volumeLevel;
  int get countdownVolumeLevel => _countdownVolumeLevel;
  Map<String, dynamic> get playersData => _playersData;

  // Game initialization
  void setupGame() {
    _playerLives = _initialLives;
    _programLives = _initialLives;
    _scorePlayer = 0;
    _scoreProgram = 0;
    _playerJackpotPoints = 0;
    _programJackpotPoints = 0;
    _consecutiveWinsPlayer = 0;
    _consecutiveWinsProgram = 0;
    
    // Start background music when game starts
    debugPrint('🎮 GAME: Setting up game - Current music: $_bgMusic');
    if (_bgMusic != null) {
      debugPrint('🎮 GAME: Starting background music from setupGame()');
      _playBackgroundMusic();
    } else {
      debugPrint('🔴 GAME: No background music set, using default');
      _bgMusic = "sunrise1.mp3";
      _playBackgroundMusic();
    }
    
    safeNotify();
  }

  // Timer methods
  void startTimer() {
    _minutes = 0;
    _seconds = 0;
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _seconds++;
      if (_seconds == 60) {
        _seconds = 0;
        _minutes++;
      }
      safeNotify();
    });
  }

  void stopTimer() {
    _gameTimer?.cancel();
  }

  void resetTimer() {
    _gameTimer?.cancel();
    _minutes = 0;
    _seconds = 0;
    safeNotify();
  }

  // Player management
  void selectPlayer(String playerName) {
    _currentPlayer = playerName;
    _playerSelected = true;
    safeNotify();
  }

  Future<void> addNewPlayer(String playerName) async {
    if (playerName.isNotEmpty && !_playersData['players'].containsKey(playerName)) {
      _playersData['players'][playerName] = [];
      await _savePlayersData();
      selectPlayer(playerName);
    }
  }

  Future<void> deletePlayer(String playerName) async {
    if (_playersData['players'].containsKey(playerName)) {
      _playersData['players'].remove(playerName);
      await _savePlayersData();
      
      if (_currentPlayer == playerName) {
        _currentPlayer = "";
        _playerSelected = false;
      }
      safeNotify();
    }
  }

  // Settings application
  void applySettings({
    int? lives,
    String? difficulty,
    String? bgMusic,
    String? bgImage,
    String? sineshifterSound,
    int? volume,
    int? countdownVolume,
    bool saveToStorage = true, // Add flag to save settings automatically
  }) {
    debugPrint('⚙️ SETTINGS: Applying settings...');
    bool musicChanged = false;
    
    if (lives != null) {
      _initialLives = lives;
      _playerLives = lives;
      _programLives = lives;
      debugPrint('⚙️ SETTINGS: Lives set to $lives');
    }
    if (difficulty != null) {
      _difficulty = difficulty;
      debugPrint('⚙️ SETTINGS: Difficulty set to $difficulty');
    }
    if (bgMusic != null) {
      debugPrint('🎵 SETTINGS: Background music changing from "$_bgMusic" to "$bgMusic"');
      _bgMusic = bgMusic;
      musicChanged = true;
      debugPrint('🎵 SETTINGS: Music changed flag set to true');
    }
    if (bgImage != null) {
      String fullPath = 'assets/bg_img/$bgImage';
      debugPrint('🖼️ SETTINGS: Background image set to $fullPath');
      _bgImage = fullPath;
    }
    if (sineshifterSound != null) {
      _sineshifterSound = sineshifterSound;
      debugPrint('🔊 SETTINGS: Sineshifter sound set to $sineshifterSound');
    }
    if (volume != null) {
      debugPrint('🔈 SETTINGS: Background music volume changing from $_volumeLevel% to $volume%');
      _volumeLevel = volume;
      _setVolume();
    }
    if (countdownVolume != null) {
      debugPrint('🔈 SETTINGS: Countdown volume changing from $_countdownVolumeLevel% to $countdownVolume%');
      _countdownVolumeLevel = countdownVolume;
      // Update AudioManager countdown volume category
      _audioManager.setCategoryVolume(AudioType.criticalEffect, countdownVolume / 100.0);
    }
    
    safeNotify();
    debugPrint('✅ SETTINGS: All settings applied successfully');
    
    // Start/restart background music if it changed
    if (musicChanged && _bgMusic != null) {
      debugPrint('🎵 SETTINGS: Starting background music: $_bgMusic');
      _playBackgroundMusic();
    }
    
    // Automatically save settings to storage
    if (saveToStorage) {
      _saveCurrentSettings();
    }
  }
  
  // Save current settings to storage
  Future<void> _saveCurrentSettings() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/settings.json');
      
      Map<String, dynamic> settings = {
        "volume": _volumeLevel,
        "countdown_volume": _countdownVolumeLevel,
        "bg_music": _bgMusic,
        "bg_image": _bgImage?.replaceAll('assets/bg_img/', ''), // Save just filename
        "sineshifter_sound": _sineshifterSound,
        "lives": _initialLives,
        "difficulty": _difficulty,
      };
      
      await file.writeAsString(json.encode(settings));
      debugPrint('💾 SAVE: Settings saved to storage successfully');
    } catch (e) {
      debugPrint('🔴 SAVE ERROR: Failed to save settings: $e');
    }
  }

  // Method to test if background music can be played
  Future<bool> testBackgroundMusic(String musicFile) async {
    try {
      debugPrint('🎧 MUSIC TEST: Testing music file: $musicFile');
      await _backgroundMusicPlayer.stop();
      // Normaliser le chemin du fichier
      String assetPath = 'assets/bg_music/$musicFile';
      await _backgroundMusicPlayer.play(AssetSource(assetPath));
      await _backgroundMusicPlayer.setReleaseMode(ReleaseMode.loop);
      await _setVolume();
      debugPrint('✅ MUSIC TEST: Successfully playing $musicFile');
      return true;
    } catch (e) {
      debugPrint('🔴 MUSIC TEST: Failed to play $musicFile: $e');
      return false;
    }
  }

  // Audio methods
  Future<void> _playBackgroundMusic() async {
    if (_bgMusic != null) {
      try {
        debugPrint('🎵 MUSIC: Playing background music with AudioManager: $_bgMusic');

        // Use the new AudioManager for background music
        await _audioManager.playBackgroundMusic(
          'bg_music/$_bgMusic',
          volume: _volumeLevel / 100.0,
        );

        debugPrint('✅ MUSIC: Background music started with AudioManager: $_bgMusic');
      } catch (e) {
        debugPrint('🔴 MUSIC: Error with AudioManager, falling back to legacy player: $e');

        // Fallback to legacy player
        try {
          await _backgroundMusicPlayer.stop();
          await _backgroundMusicPlayer.play(AssetSource('bg_music/$_bgMusic'));
          await _backgroundMusicPlayer.setReleaseMode(ReleaseMode.loop);
          await _setVolume();
          debugPrint('✅ MUSIC: Fallback to legacy player successful');
        } catch (legacyError) {
          debugPrint('🔴 MUSIC: Legacy player also failed: $legacyError');

          // Try default music as last resort
          if (_bgMusic != 'sunrise1.mp3') {
            debugPrint('🔄 MUSIC: Attempting default music: sunrise1.mp3');
            try {
              await _audioManager.playBackgroundMusic('bg_music/sunrise1.mp3');
            } catch (defaultError) {
              debugPrint('🔴 MUSIC: Even default music failed: $defaultError');
            }
          }
        }
      }
    } else {
      debugPrint('⚠️ MUSIC: No background music selected (_bgMusic is null)');
    }
  }

  Future<void> _setVolume() async {
    double volume = _volumeLevel / 100.0;

    // Update AudioManager volume
    await _audioManager.setGlobalVolume(volume);

    // Legacy players (for backward compatibility)
    await _backgroundMusicPlayer.setVolume(volume);
    await _effectsPlayer.setVolume(volume);

    debugPrint('🔊 AUDIO: Volume updated to ${_volumeLevel}% (AudioManager + legacy)');
  }

  Future<void> playEffect(String soundFile) async {
    try {
      debugPrint('🔊 AUDIO: Playing sound effect with AudioManager: $soundFile');

      // Determine the type of effect based on the file path
      AudioType effectType = _getEffectType(soundFile);

      // Use appropriate AudioManager method based on effect type
      switch (effectType) {
        case AudioType.jackpotEffect:
          await _audioManager.playJackpotEffect(soundFile);
          break;
        case AudioType.criticalEffect:
          await _audioManager.playCriticalEffect(soundFile);
          break;
        case AudioType.uiEffect:
          await _audioManager.playUIEffect(soundFile);
          break;
        default:
          await _audioManager.playGameEffect(soundFile);
      }

      debugPrint('✅ AUDIO: Sound effect started successfully with AudioManager (type: ${effectType.name})');
    } catch (e) {
      debugPrint('🔴 AUDIO: Error with AudioManager, falling back to legacy player: $e');

      // Fallback to legacy player
      try {
        await _effectsPlayer.play(AssetSource(soundFile));
        debugPrint('✅ AUDIO: Fallback to legacy player successful');
      } catch (legacyError) {
        debugPrint('🔴 AUDIO: Legacy player also failed: $legacyError');
      }
    }
  }

  /// Determines the audio type based on the file path
  AudioType _getEffectType(String soundFile) {
    if (soundFile.contains('jackpot')) {
      return AudioType.jackpotEffect;
    } else if (soundFile.contains('gameover') ||
               soundFile.contains('3_2_1_kids') ||
               soundFile.contains('3_2_go/') ||
               soundFile.contains('eddars_global')) {
      // All countdown and critical sounds use criticalEffect type
      // This allows separate volume control for countdown sounds
      return AudioType.criticalEffect;
    } else if (soundFile.contains('ui_') || soundFile.contains('button_')) {
      return AudioType.uiEffect;
    } else {
      return AudioType.gameEffect;
    }
  }

  /// Play multiple sound effects simultaneously
  Future<void> playMultipleEffects(List<String> soundFiles) async {
    debugPrint('🎵 AUDIO: Playing ${soundFiles.length} simultaneous effects');

    // Play all sounds in parallel
    List<Future<void>> playTasks = soundFiles.map((soundFile) => playEffect(soundFile)).toList();

    try {
      await Future.wait(playTasks);
      debugPrint('✅ AUDIO: All ${soundFiles.length} effects started successfully');
    } catch (e) {
      debugPrint('🔴 AUDIO: Error playing multiple effects: $e');
    }
  }

  /// Play a sound effect with a specific delay
  Future<void> playEffectWithDelay(String soundFile, Duration delay) async {
    debugPrint('🕐 AUDIO: Scheduling $soundFile to play in ${delay.inMilliseconds}ms');

    Timer(delay, () {
      playEffect(soundFile);
    });
  }

  /// Play a sequence of sound effects with delays
  Future<void> playEffectSequence(List<({String file, Duration delay})> sequence) async {
    debugPrint('🎼 AUDIO: Playing sequence of ${sequence.length} effects');

    for (var item in sequence) {
      playEffectWithDelay(item.file, item.delay);
    }
  }

  /// Stop all audio (emergency stop)
  Future<void> stopAllAudio() async {
    debugPrint('🛑 AUDIO: Emergency stop - stopping all audio');

    try {
      await _audioManager.stopAll();
      await _backgroundMusicPlayer.stop();
      await _effectsPlayer.stop();
      debugPrint('✅ AUDIO: All audio stopped successfully');
    } catch (e) {
      debugPrint('🔴 AUDIO: Error stopping audio: $e');
    }
  }

  /// Play technical error whistle sound (siflet.mp3)
  Future<void> playTechnicalErrorSound() async {
    debugPrint('🚨 AUDIO: Playing technical error whistle sound');

    try {
      // Use critical effect type for technical errors (high priority, non-interruptible)
      await _audioManager.playCriticalEffect('audio/siflet.mp3');
      debugPrint('✅ AUDIO: Technical error sound played successfully');
    } catch (e) {
      debugPrint('🔴 AUDIO: Error playing technical error sound: $e');

      // Fallback to legacy player
      try {
        await _effectsPlayer.play(AssetSource('audio/siflet.mp3'));
        debugPrint('✅ AUDIO: Technical error sound played with legacy player');
      } catch (legacyError) {
        debugPrint('🔴 AUDIO: Legacy player also failed for technical error sound: $legacyError');
      }
    }
  }

  /// Get audio system status for debugging
  void printAudioStatus() {
    debugPrint('📊 GAME AUDIO STATUS:');
    debugPrint('  Volume Level: $_volumeLevel%');
    debugPrint('  Background Music: $_bgMusic');
    debugPrint('  Sineshifter Sound: $_sineshifterSound');

    _audioManager.printStatus();
  }

  // Game logic methods
  void updateScore({required bool playerWon}) {
    if (playerWon) {
      _scorePlayer++;
      _consecutiveWinsPlayer++;
      _consecutiveWinsProgram = 0;
      _handleJackpotPoints(winner: "player");
    } else {
      _scoreProgram++;
      _consecutiveWinsProgram++;
      _consecutiveWinsPlayer = 0;
      _handleJackpotPoints(winner: "program");
    }
    safeNotify();
  }

  void updateLives({required bool playerLostLife}) {
    if (playerLostLife) {
      _playerLives--;
      // Check if player can buy extra life AFTER losing the life
      if (_playerLives <= 0 && _playerJackpotPoints >= 3000) {
        debugPrint('🎆 JACKPOT: Player lost last life but has ${_playerJackpotPoints} jackpot points - buying extra life!');
        _buyExtraLife(isPlayer: true);
      }
    } else {
      _programLives--;
      // Check if program can buy extra life AFTER losing the life
      if (_programLives <= 0 && _programJackpotPoints >= 3000) {
        debugPrint('🎆 JACKPOT: Program lost last life but has ${_programJackpotPoints} jackpot points - buying extra life!');
        _buyExtraLife(isPlayer: false);
      }
    }
    safeNotify();
  }

  void _handleJackpotPoints({required String winner}) {
    if (winner == "player") {
      if (_consecutiveWinsPlayer == 1) {
        _playerJackpotPoints += 100;
      } else if (_consecutiveWinsPlayer >= 2) {
        _playerJackpotPoints += 2500;
        // Play jackpot sound for big points bonus
        playEffect('audio/jackpot_start.mp3');
        debugPrint('🎉 JACKPOT: Player earned 2500 jackpot points!');
      }
    } else {
      if (_consecutiveWinsProgram == 1) {
        _programJackpotPoints += 100;
      } else if (_consecutiveWinsProgram >= 2) {
        _programJackpotPoints += 2500;
        // Play jackpot sound for big points bonus
        playEffect('audio/jackpot_start.mp3');
        debugPrint('🎉 JACKPOT: Program earned 2500 jackpot points!');
      }
    }
    safeNotify();
  }

  void _buyExtraLife({required bool isPlayer}) {
    if (isPlayer && _playerJackpotPoints >= 3000) {
      _playerJackpotPoints -= 3000;
      _playerLives = 1; // Set to 1 life instead of incrementing
      // Play jackpot end sound for buying extra life
      playEffect('audio/jackpot_end.mp3');
      debugPrint('🎆 JACKPOT: Player bought extra life! Lives: $_playerLives, Remaining jackpot: $_playerJackpotPoints');
    } else if (!isPlayer && _programJackpotPoints >= 3000) {
      _programJackpotPoints -= 3000;
      _programLives = 1; // Set to 1 life instead of incrementing
      // Play jackpot end sound for buying extra life
      playEffect('audio/jackpot_end.mp3');
      debugPrint('🎆 JACKPOT: Program bought extra life! Lives: $_programLives, Remaining jackpot: $_programJackpotPoints');
    }
    safeNotify();
  }

  // Save/Load game results
  Future<void> saveGameResult({
    required String finalResult,
    required int playerPoints,
    required int programPoints,
    required int playerJackpot,
    required int programJackpot,
  }) async {
    if (_currentPlayer.isNotEmpty) {
      String elapsedTime = "${_minutes.toString().padLeft(2, '0')}:${_seconds.toString().padLeft(2, '0')}";
      
      Map<String, dynamic> gameData = {
        "result": finalResult,
        "player_points": playerPoints,
        "program_points": programPoints,
        "player_jackpot": playerJackpot,
        "program_jackpot": programJackpot,
        "elapsed_time": elapsedTime,
        "timing": DateTime.now().toString().substring(0, 19),
      };
      
      if (!_playersData["players"].containsKey(_currentPlayer)) {
        _playersData["players"][_currentPlayer] = [];
      }
      _playersData["players"][_currentPlayer].add(gameData);
      
      await _savePlayersData();
    }
  }

  // File operations
  Future<void> loadPlayersData() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/result.json');
      
      if (await file.exists()) {
        String contents = await file.readAsString();
        _playersData = json.decode(contents);
      }
    } catch (e) {
      debugPrint('Error loading players data: $e');
      _playersData = {"players": {}};
    }
    safeNotify();
  }

  Future<void> _savePlayersData() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/result.json');
      await file.writeAsString(json.encode(_playersData));
    } catch (e) {
      debugPrint('Error saving players data: $e');
    }
  }

  // Restart game
  void restartGame() {
    debugPrint('🔄 RESTART: Restarting game - Current music: $_bgMusic');
    stopTimer();
    
    // Ensure background music is set before setup
    if (_bgMusic == null) {
      debugPrint('🔄 RESTART: No music set, loading from settings');
      _loadSettingsOnStartup().then((_) {
        setupGame();
        resetTimer();
      });
    } else {
      setupGame();
      resetTimer();
    }
  }

  @override
  void dispose() {
    _gameTimer?.cancel();
    _backgroundMusicPlayer.dispose();
    _effectsPlayer.dispose();
    super.dispose();
  }

}