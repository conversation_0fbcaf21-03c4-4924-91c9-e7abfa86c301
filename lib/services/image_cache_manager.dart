import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'dart:async';
import 'dart:io';

/// Service to manage image cache and prevent memory issues during long gameplay sessions
class ImageCacheManager {
  static const int _maxCacheSize = 50; // Maximum number of images to keep in cache
  static const int _maxCacheSizeBytes = 50 * 1024 * 1024; // 50MB cache limit
  static const int _criticalMemoryThreshold = 80; // Percentage for critical cleanup
  static const int _warningMemoryThreshold = 70; // Percentage for warning cleanup

  /// Game-specific images that need frequent management
  static const List<String> gameImages = [
    'assets/images/you_win.png',
    'assets/images/you_lose.png',
    'assets/images/egal.png',
    'assets/images/sheriff.png',
    'assets/images/ciseaux.png',
    'assets/images/pierre.png',
    'assets/images/papier.png',
    'assets/images/3.png',
    'assets/images/2.png',
    'assets/images/1.png',
    'assets/images/gameover.png',
    'assets/images/play.png',
    'assets/images/quit.png',
  ];

  /// Essential images that should never be evicted
  static const List<String> essentialImages = [
    'assets/images/you_win.png',
    'assets/images/you_lose.png',
    'assets/images/egal.png',
    'assets/images/sheriff.png',
    'assets/images/ciseaux.png',
    'assets/images/pierre.png',
    'assets/images/papier.png',
    'assets/images/gameover.png',
    'assets/images/play.png',
    'assets/images/quit.png',
  ];

  static ImageCacheManager? _instance;
  Timer? _memoryMonitorTimer;
  int _cleanupCount = 0;

  /// ⏸️ Pause flag to temporarily disable cleanups (e.g., while Game Over dialog is open)
  bool _paused = false;
  void pause()  => _paused = true;
  void resume() => _paused = false;

  /// Singleton instance
  static ImageCacheManager get instance {
    _instance ??= ImageCacheManager._();
    return _instance!;
  }

  ImageCacheManager._();

  /// Initialize the cache manager with optimal settings
  void initialize() {
    final imageCache = PaintingBinding.instance.imageCache;
    imageCache.maximumSize = _maxCacheSize;
    imageCache.maximumSizeBytes = _maxCacheSizeBytes;
    _startMemoryMonitoring();
    debugPrint('🖼️ IMAGE_CACHE: Initialized with $_maxCacheSize images, ${_maxCacheSizeBytes ~/ (1024 * 1024)}MB limit');
    debugPrint('🖼️ IMAGE_CACHE: Memory monitoring started');
  }

  void _startMemoryMonitoring() {
    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _checkMemoryPressure();
    });
  }

  void _checkMemoryPressure() {
    try {
      final imageCache = PaintingBinding.instance.imageCache;
      final memoryUsagePercent = (imageCache.currentSizeBytes / imageCache.maximumSizeBytes * 100).round();
      final cacheUsagePercent  = (imageCache.currentSize / imageCache.maximumSize * 100).round();

      debugPrint('📊 IMAGE_CACHE: Memory monitoring - Cache: $cacheUsagePercent%, Memory: $memoryUsagePercent%');

      if (memoryUsagePercent >= _criticalMemoryThreshold || cacheUsagePercent >= _criticalMemoryThreshold) {
        debugPrint('🚨 IMAGE_CACHE: Critical memory pressure detected! Performing emergency cleanup');
        _performEmergencyCleanup();
      } else if (memoryUsagePercent >= _warningMemoryThreshold || cacheUsagePercent >= _warningMemoryThreshold) {
        debugPrint('⚠️ IMAGE_CACHE: Warning memory pressure detected. Performing maintenance cleanup');
        performMaintenanceCleaning();
      }

      _checkSystemMemory();
    } catch (e) {
      debugPrint('🔴 IMAGE_CACHE: Error during memory pressure check: $e');
    }
  }

  void _checkSystemMemory() {
    if (Platform.isAndroid || Platform.isIOS) {
      final imageCache = PaintingBinding.instance.imageCache;
      final currentSizeMB = imageCache.currentSizeBytes / (1024 * 1024);
      if (currentSizeMB > 50) {
        debugPrint('📱 IMAGE_CACHE: Mobile platform - large cache detected (${currentSizeMB.toStringAsFixed(1)}MB)');
        performMaintenanceCleaning();
      }
    }
  }

  /// ✅ Proper preloading using precacheImage (requires BuildContext)
  Future<void> preloadGameImages(BuildContext context) async {
    debugPrint('🖼️ IMAGE_CACHE: Preloading game images...');
    for (final imagePath in gameImages) {
      try {
        await precacheImage(AssetImage(imagePath), context);
        debugPrint('🖼️ IMAGE_CACHE: Precached $imagePath');
      } catch (e) {
        debugPrint('🔴 IMAGE_CACHE: Failed to precache $imagePath: $e');
      }
    }
    debugPrint('🖼️ IMAGE_CACHE: Preloading completed');
  }

  /// Clean up cache periodically to prevent memory issues
  void performMaintenanceCleaning() {
    if (_paused) {
      debugPrint('🟡 IMAGE_CACHE: Maintenance cleanup paused');
      return;
    }

    final imageCache = PaintingBinding.instance.imageCache;
    final currentSize = imageCache.currentSize;
    final currentSizeBytes = imageCache.currentSizeBytes;

    debugPrint('🧹 IMAGE_CACHE: Current cache: $currentSize images, ${currentSizeBytes ~/ (1024 * 1024)}MB');

    if (currentSize > (_maxCacheSize * 0.8) || currentSizeBytes > (_maxCacheSizeBytes * 0.8)) {
      debugPrint('🧹 IMAGE_CACHE: Cache getting full, performing maintenance cleanup...');
      _performSmartCleanup();
      _cleanupCount++;
      debugPrint('🧹 IMAGE_CACHE: Maintenance cleanup completed (cleanup #$_cleanupCount)');
    }
  }

  /// Perform emergency cleanup when memory pressure is critical
  void _performEmergencyCleanup() {
    if (_paused) {
      debugPrint('🟡 IMAGE_CACHE: Emergency cleanup paused');
      return;
    }

    debugPrint('🚨 IMAGE_CACHE: EMERGENCY CLEANUP - Critical memory pressure!');
    final imageCache = PaintingBinding.instance.imageCache;
    imageCache.clear();
    _preloadEssentialImagesSync();
    debugPrint('🚨 IMAGE_CACHE: Emergency cleanup completed - cache cleared and essential images reloaded');
  }

  void _performSmartCleanup() {
    final imageCache = PaintingBinding.instance.imageCache;

    for (final imagePath in gameImages) {
      if (!essentialImages.contains(imagePath)) {
        imageCache.evict(AssetImage(imagePath));
        debugPrint('🗑️ IMAGE_CACHE: Evicted non-essential image: $imagePath');
      }
    }

    final currentUsage = imageCache.currentSizeBytes / imageCache.maximumSizeBytes;
    if (currentUsage > 0.7) {
      debugPrint('🧹 IMAGE_CACHE: Still high usage after smart cleanup, performing full cleanup');
      imageCache.clear();
      _preloadEssentialImagesSync();
    }
  }

  /// Preload essential images synchronously (for emergency situations)
  void _preloadEssentialImagesSync() {
    debugPrint('🔄 IMAGE_CACHE: Preloading essential images synchronously...');
    for (final imagePath in essentialImages) {
      try {
        final image = AssetImage(imagePath);
        image.resolve(const ImageConfiguration());
        debugPrint('✅ IMAGE_CACHE: Preloaded essential: $imagePath');
      } catch (e) {
        debugPrint('🔴 IMAGE_CACHE: Failed to preload essential $imagePath: $e');
      }
    }
  }

  /// Clear cache of specific images that are no longer needed
  void clearSpecificImages(List<String> imagePaths) {
    final imageCache = PaintingBinding.instance.imageCache;
    for (final imagePath in imagePaths) {
      imageCache.evict(AssetImage(imagePath));
      debugPrint('🗑️ IMAGE_CACHE: Evicted $imagePath');
    }
  }

  /// Force clear all images from cache
  void forceClearAll() {
    final imageCache = PaintingBinding.instance.imageCache;
    imageCache.clear();
    debugPrint('🗑️ IMAGE_CACHE: Force cleared all images from cache');
  }

  /// Get current cache statistics
  Map<String, dynamic> getCacheStats() {
    final imageCache = PaintingBinding.instance.imageCache;
    return {
      'currentSize': imageCache.currentSize,
      'maximumSize': imageCache.maximumSize,
      'currentSizeBytes': imageCache.currentSizeBytes,
      'maximumSizeBytes': imageCache.maximumSizeBytes,
      'utilizationPercent': (imageCache.currentSize / imageCache.maximumSize * 100).round(),
      'memoryUtilizationPercent': (imageCache.currentSizeBytes / imageCache.maximumSizeBytes * 100).round(),
    };
  }

  /// Reload essential images after cache clear
  void _reloadEssentialImages() {
    debugPrint('🔄 IMAGE_CACHE: Essential images will be reloaded on-demand');
  }

  /// Method to be called periodically during gameplay
  void periodicMaintenance() {
    final stats = getCacheStats();
    debugPrint('📊 IMAGE_CACHE: Periodic check - ${stats['currentSize']} images (${stats['utilizationPercent']}%), ${stats['memoryUtilizationPercent']}% memory');
    if (stats['utilizationPercent'] > 80 || stats['memoryUtilizationPercent'] > 80) {
      performMaintenanceCleaning();
    }
  }

  /// Handle low memory warnings from the system
  void handleLowMemoryWarning() {
    debugPrint('🚨 IMAGE_CACHE: Low memory warning received from system!');
    _performEmergencyCleanup();
  }

  /// Prepare for game round - ensure critical images are available
  Future<void> prepareForGameRound() async {
    try {
      debugPrint('🎮 IMAGE_CACHE: Preparing for game round...');
      for (final imagePath in essentialImages) {
        final image = AssetImage(imagePath);
        final stream = image.resolve(const ImageConfiguration());
        final completer = Completer<void>();
        late ImageStreamListener listener;
        listener = ImageStreamListener(
          (info, synchronousCall) {
            stream.removeListener(listener);
            if (!completer.isCompleted) completer.complete();
          },
          onError: (error, stackTrace) {
            stream.removeListener(listener);
            if (!completer.isCompleted) {
              debugPrint('🔴 IMAGE_CACHE: Error preloading $imagePath: $error');
              completer.complete();
            }
          },
        );
        stream.addListener(listener);
        await completer.future.timeout(
          const Duration(seconds: 2),
          onTimeout: () {
            stream.removeListener(listener);
            debugPrint('⏰ IMAGE_CACHE: Timeout preloading $imagePath');
          },
        );
      }
      debugPrint('✅ IMAGE_CACHE: Game round preparation completed');
    } catch (e) {
      debugPrint('🔴 IMAGE_CACHE: Error preparing for game round: $e');
    }
  }

  /// Cleanup when game ends or app is backgrounded
  void performGameEndCleanup() {
    debugPrint('🧹 IMAGE_CACHE: Performing game end cleanup...');
    try {
      final imageCache = PaintingBinding.instance.imageCache;
      for (final imagePath in gameImages) {
        if (!essentialImages.contains(imagePath)) {
          imageCache.evict(AssetImage(imagePath));
        }
      }
      debugPrint('✅ IMAGE_CACHE: Game end cleanup completed');
    } catch (e) {
      debugPrint('🔴 IMAGE_CACHE: Error during game end cleanup: $e');
    }
  }

  /// Dispose the image cache manager
  void dispose() {
    debugPrint('🧹 IMAGE_CACHE: Disposing image cache manager...');
    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = null;
    forceClearAll();
    _instance = null;
    debugPrint('✅ IMAGE_CACHE: Image cache manager disposed');
  }
}
