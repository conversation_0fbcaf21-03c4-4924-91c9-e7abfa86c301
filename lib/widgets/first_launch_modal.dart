import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FirstLaunchModal extends StatefulWidget {
  final VoidCallback onStartTraining;
  final VoidCallback onSkip;

  const FirstLaunchModal({
    super.key,
    required this.onStartTraining,
    required this.onSkip,
  });

  @override
  State<FirstLaunchModal> createState() => _FirstLaunchModalState();
}

class _FirstLaunchModalState extends State<FirstLaunchModal>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Opacity(
                opacity: _fadeAnimation.value,
                child: Container(
                  margin: const EdgeInsets.all(20),
                  padding: const EdgeInsets.all(30),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFF1E3A8A), // Deep blue
                        Color(0xFF3B82F6), // Blue
                        Color(0xFF8B5CF6), // Purple
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Welcome icon
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.waving_hand,
                          size: 60,
                          color: Colors.white,
                        ),
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // Title
                      const Text(
                        'Welcome to Rock Paper Scissors!',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 15),
                      
                      // Description
                      const Text(
                        'This game uses hand gesture recognition to play!\n\n'
                        '✊ Make a fist for ROCK\n'
                        '✋ Open palm for PAPER\n'
                        '✌️ Peace sign for SCISSORS\n\n'
                        'Would you like to try the training mode first to learn how it works?',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 30),
                      
                      // Buttons
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                _markAsShown();
                                widget.onSkip();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white.withOpacity(0.2),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 15),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  side: const BorderSide(color: Colors.white, width: 1),
                                ),
                              ),
                              child: const Text(
                                'Skip',
                                style: TextStyle(fontSize: 16),
                              ),
                            ),
                          ),
                          
                          const SizedBox(width: 15),
                          
                          Expanded(
                            flex: 2,
                            child: ElevatedButton(
                              onPressed: () {
                                _markAsShown();
                                widget.onStartTraining();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white,
                                foregroundColor: const Color(0xFF1E3A8A),
                                padding: const EdgeInsets.symmetric(vertical: 15),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                elevation: 5,
                              ),
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.school, size: 20),
                                  SizedBox(width: 8),
                                  Text(
                                    'Start Training',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Future<void> _markAsShown() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('first_launch_modal_shown', true);
      debugPrint('✅ FIRST LAUNCH: Modal marked as shown');
    } catch (e) {
      debugPrint('🔴 FIRST LAUNCH: Error saving modal state: $e');
    }
  }

  static Future<bool> shouldShow() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final shown = prefs.getBool('first_launch_modal_shown') ?? false;
      debugPrint('🔍 FIRST LAUNCH: Modal should show: ${!shown}');
      return !shown;
    } catch (e) {
      debugPrint('🔴 FIRST LAUNCH: Error checking modal state: $e');
      return true; // Show by default if we can't check
    }
  }
}
