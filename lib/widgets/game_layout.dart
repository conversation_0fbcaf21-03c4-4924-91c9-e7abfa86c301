import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:provider/provider.dart';
import 'package:camera/camera.dart';
import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/services.dart';
import '../providers/game_state_provider.dart';
import '../services/gesture_detector.dart' as gesture_service;
import '../services/image_cache_manager.dart';
import '../widgets/life_icons_row.dart';
import '../widgets/score_display.dart';
import '../widgets/jackpot_display.dart';
import '../widgets/game_timer.dart';
import '../widgets/player_info.dart';




import '../widgets/player_table_dialog.dart';
import '../services/audio_manager.dart' show AudioManager;




class GameLayout extends StatefulWidget {
final VoidCallback onSettingsPressed;




const GameLayout({
  super.key,
  required this.onSettingsPressed,
});




@override
State<GameLayout> createState() => _GameLayoutState();
}




class _GameLayoutState extends State<GameLayout> with TickerProviderStateMixin {
bool _gameOverOpen = false;
late gesture_service.HandGestureDetector gestureDetector;
bool _isDetecting = false;
bool _gameStarted = false;
bool _cameraError = false;
String _cameraErrorMessage = '';
StreamSubscription<gesture_service.GestureType>? _gestureSubscription;
 // Animation controllers
late AnimationController _fadeController;
late AnimationController _scaleController;
 // Game state
final List<String> _countdownImages = ['3.png', '2.png', '1.png'];
final List<String> _choiceImages = ['ciseaux.png', 'pierre.png', 'papier.png'];
 int _currentCountdownIndex = 0;
bool _showingCountdown = false;
bool _showingResult = false;
String? _currentResultImage;
String? _currentChoiceImage;
String? _detectedGesture;

// Synchronization variables to prevent race conditions
bool _roundInProgress = false;
bool _isFirstRound = true; // Track if this is the first round
bool _showingCancelledMessage = false; // Track if showing cancelled message
Timer? _countdownTimer;
Timer? _resultTimer;
Timer? _nextRoundTimer;
Timer? _cancelledMessageTimer;
Timer? _cacheMaintenanceTimer; // Timer for periodic cache maintenance

// Memory management flags
bool _isDisposing = false;
bool _isDisposed = false;
final Set<Timer> _activeTimers = <Timer>{}; // Track all active timers
final List<StreamSubscription> _subscriptions = []; // Track all subscriptions




@override
void initState() {
  super.initState();

  debugPrint('🎮 GAME_LAYOUT: Initializing game layout...');

  // Initialize image cache manager
  ImageCacheManager.instance.initialize();
  
  // Start periodic cache maintenance using tracked timer (every 2 minutes)
  _cacheMaintenanceTimer = _createTrackedPeriodicTimer(
    const Duration(minutes: 2), 
    (_) {
      if (!_isDisposing && !_isDisposed) {
        ImageCacheManager.instance.periodicMaintenance();
      }
    }
  );

   // Initialize animation controllers
  _fadeController = AnimationController(
    duration: const Duration(milliseconds: 100),
    vsync: this,
  );
   _scaleController = AnimationController(
    duration: const Duration(milliseconds: 500),
    vsync: this,
  );
   // Initialize gesture detector with error handling
  gestureDetector = gesture_service.HandGestureDetector();
   // Defer complex initialization to prevent main thread blocking
  WidgetsBinding.instance.addPostFrameCallback((_) {
    if (!_isDisposing && !_isDisposed) {
      _initializeWithErrorHandling();
    }
  });
  
  debugPrint('✅ GAME_LAYOUT: Game layout initialized');
}
 Future<void> _initializeWithErrorHandling() async {
  try {
    // Initialize gesture detector asynchronously
    await _initializeGestureDetector();
    
    // Preload critical game images
    await ImageCacheManager.instance.preloadGameImages(context);
  
    // Load players data with error handling
    await _loadPlayersDataSafely();
  } catch (e) {
    debugPrint('🔴 GAME ERROR: Error during game initialization: $e');
    // Show fallback UI if needed
    _showErrorSnackBar('Initialization completed with some limitations');
  }
}
 Future<void> _loadPlayersDataSafely() async {
  try {
    Provider.of<GameStateProvider>(context, listen: false).loadPlayersData();
  } catch (e) {
    debugPrint('🔴 GAME ERROR: Error loading players data: $e');
    // Continue without player data - app should not crash
  }
}




Future<void> _initializeGestureDetector() async {
  try {
    final success = await gestureDetector.initialize();
    if (!success) {
      debugPrint('🔴 CAMERA ERROR: Gesture detector initialization failed');
      if (mounted) { setState(() {
        _cameraError = true;
        _cameraErrorMessage = 'Erreur de la caméra. Veuillez vérifier les permissions et redémarrer l\'application.';
      }); }
      _showErrorSnackBar('Erreur de la caméra. Redémarrez l\'application.');
    } else {
      debugPrint('🎮 CAMERA: Gesture detector initialized successfully');
      // Listen to gesture stream if available
      _setupGestureListener();
    }
  } catch (e) {
    debugPrint('🔴 CAMERA ERROR: Error during gesture detector initialization: $e');
    if (mounted) { setState(() {
      _cameraError = true;
      _cameraErrorMessage = 'Erreur de la caméra: $e';
    }); }
    _showErrorSnackBar('Erreur de la caméra. Redémarrez l\'application.');
  }
}
 // Set up gesture listener for real-time detection
void _setupGestureListener() {
  try {
    _gestureSubscription?.cancel();
  
    debugPrint('🎮 GAME: Setting up gesture listener');
  
    // Try to use the gesture stream if available
    if (gestureDetector.gestureStream != null) {
      debugPrint('🎮 GAME: Using gesture stream');
      _gestureSubscription = gestureDetector.gestureStream!.listen(
        (gesture) {
          debugPrint('🎮 GAME: Stream received gesture: $gesture');
          if (gesture != gesture_service.GestureType.none &&
              gesture != gesture_service.GestureType.unknown) {
            _onGestureDetected(gesture);
          }
        },
        onError: (error) {
          debugPrint('🔴 GAME: Gesture stream error: $error');
          // Fall back to polling on stream error
          _startPollingFallback();
        }
      );
    } else {
      debugPrint('🎮 GAME: Gesture stream not available, using polling fallback');
      _startPollingFallback();
    }
  
    // Always set up the polling mechanism as a secondary fallback
    // This ensures detection works even if the stream has issues
    if (mounted && _isDetecting) {
      Timer(const Duration(milliseconds: 1000), () {
        if (mounted && _isDetecting) {
          _detectGesture();
        }
      });
    }
  } catch (e) {
    debugPrint('🔴 GESTURE DETECTION: Error setting up gesture listener: $e');
    // Fall back to polling if stream setup fails
    _startPollingFallback();
  }
}
 // Start real gesture detection polling
void _startPollingFallback() {
  debugPrint('🎮 GAME: Starting gesture detection polling');
  
  final pollingTimer = _createTrackedPeriodicTimer(const Duration(milliseconds: 300), (timer) {
    if (!_isDetecting || !mounted || _isDisposing || _isDisposed) {
      timer.cancel();
      return;
    }
  
    try {
      // Vérification de la détection réelle des gestes
      String playerGesture = gestureDetector.getGesture();
      if (['papier', 'pierre', 'ciseaux'].contains(playerGesture)) {
        debugPrint('🎮 GAME: Gesture detected: $playerGesture');
        timer.cancel();
        _processValidGesture(playerGesture);
      }
    } catch (e) {
      debugPrint('🔴 GAME: Erreur de détection de geste: $e');
    }
  });
}
 // Handle gesture detected from stream
void _onGestureDetected(gesture_service.GestureType gesture) {
  String gestureString;
  switch (gesture) {
    case gesture_service.GestureType.rock:
      gestureString = 'pierre';
      break;
    case gesture_service.GestureType.paper:
      gestureString = 'papier';
      break;
    case gesture_service.GestureType.scissors:
      gestureString = 'ciseaux';
      break;
    default:
      return; // Ignore invalid gestures
  }
   debugPrint('🎮 GAME: Processing stream gesture: $gestureString');
  _processValidGesture(gestureString);
}
 // Handle detected gesture
void _handleDetectedGesture(gesture_service.GestureType gesture) {
  if (!_isDetecting) return;
   debugPrint('🎮 GAME: Handling detected gesture: $gesture');
   String? newDetectedGesture;
   // Use if-else instead of switch to avoid constant context issues
  if (gesture == gesture_service.GestureType.rock) {
    newDetectedGesture = "pierre";
  } else if (gesture == gesture_service.GestureType.paper) {
    newDetectedGesture = "papier";
  } else if (gesture == gesture_service.GestureType.scissors) {
    newDetectedGesture = "ciseaux";
  } else if (gesture == gesture_service.GestureType.none ||
             gesture == gesture_service.GestureType.unknown) {
    newDetectedGesture = null;
  }
   debugPrint('🎮 GAME: Mapped gesture to: $newDetectedGesture');
   // Only update state if the gesture has changed
  if (_detectedGesture != newDetectedGesture) {
    if (mounted) { setState(() {
      _detectedGesture = newDetectedGesture;
    }); }
    debugPrint('🎮 GAME: Updated _detectedGesture to: $_detectedGesture');
  }
   // If we have a valid gesture, process it
  if (newDetectedGesture != null &&
      ['papier', 'pierre', 'ciseaux'].contains(newDetectedGesture)) {
    debugPrint('🎮 GAME: Valid gesture detected, processing: $newDetectedGesture');
    _processValidGesture(newDetectedGesture);
  }
}




@override
void dispose() {
  debugPrint('🧹 GAME_LAYOUT: Starting dispose process...');
  
  // Set disposing flag to prevent new operations
  _isDisposing = true;
  
  try {
    // Cancel all active timers first to prevent race conditions
    _cancelAllTimers();
    
    // Stop gesture detection and dispose detector
    if (gestureDetector.isInitialized) {
      gestureDetector.stopDetection().catchError((e) {
        debugPrint('🔴 DISPOSE: Error stopping gesture detection: $e');
      });
    }
    gestureDetector.dispose();
    
    // Cancel all subscriptions
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
    
    // Cancel gesture subscription
    _gestureSubscription?.cancel();
    
    // Dispose animation controllers
    if (_fadeController.isAnimating) {
      _fadeController.stop();
    }
    _fadeController.dispose();
    
    if (_scaleController.isAnimating) {
      _scaleController.stop();
    }
    _scaleController.dispose();
    
    // Force cleanup memory (includes image cache clearing)
    _forceMemoryCleanup();
    
    _isDisposed = true;
    debugPrint('✅ GAME_LAYOUT: Dispose completed successfully');
  } catch (e) {
    debugPrint('🔴 DISPOSE: Error during dispose: $e');
  }

  super.dispose();
}

/// Cancel all active timers safely
void _cancelAllTimers() {
  debugPrint('🧹 TIMER_CLEANUP: Cancelling all active timers...');
  
  try {
    // Cancel specific timers
    _countdownTimer?.cancel();
    _resultTimer?.cancel();
    _nextRoundTimer?.cancel();
    _cancelledMessageTimer?.cancel();
    _cacheMaintenanceTimer?.cancel();
    
    // Cancel all tracked timers
    for (final timer in _activeTimers) {
      if (timer.isActive) {
        timer.cancel();
      }
    }
    _activeTimers.clear();
    
    // Clear timer references
    _countdownTimer = null;
    _resultTimer = null;
    _nextRoundTimer = null;
    _cancelledMessageTimer = null;
    _cacheMaintenanceTimer = null;
    
    debugPrint('✅ TIMER_CLEANUP: All timers cancelled successfully');
  } catch (e) {
    debugPrint('🔴 TIMER_CLEANUP: Error cancelling timers: $e');
  }
}

/// Force memory cleanup
void _forceMemoryCleanup() {
  if (!mounted) return;
  
  // Clear state
  _currentResultImage = null;
  _currentChoiceImage = null;
  _detectedGesture = null;
  
  // Clear image cache
  try {
    // Appel synchrone à clear() car c'est la méthode correcte dans Flutter
    PaintingBinding.instance.imageCache.clear();
    debugPrint('🧹 MEMORY: Image cache cleared');
  } catch (e) {
    debugPrint('🔴 MEMORY: Error clearing image cache: $e');
  }
}

/// Create a tracked timer that will be automatically cleaned up
Timer _createTrackedTimer(Duration duration, VoidCallback callback) {
  if (_isDisposing || _isDisposed) {
    debugPrint('⚠️ TIMER: Skipping timer creation - widget is disposing/disposed');
    return Timer(Duration.zero, () {}); // Return dummy timer
  }
  
  late final Timer timer;
  timer = Timer(duration, () {
    try {
      if (!_isDisposing && !_isDisposed && mounted) {
        callback();
      }
    } catch (e) {
      debugPrint('🔴 TIMER: Error in timer callback: $e');
    } finally {
      _activeTimers.remove(timer);
    }
  });
  
  _activeTimers.add(timer);
  return timer;
}

/// Create a tracked periodic timer that will be automatically cleaned up
Timer _createTrackedPeriodicTimer(Duration duration, void Function(Timer) callback) {
  if (_isDisposing || _isDisposed) {
    debugPrint('⚠️ TIMER: Skipping periodic timer creation - widget is disposing/disposed');
    return Timer.periodic(Duration(days: 1), (_) {}); // Return dummy timer
  }
  
  final timer = Timer.periodic(duration, (timer) {
    try {
      if (!_isDisposing && !_isDisposed && mounted) {
        callback(timer);
      } else {
        timer.cancel();
        _activeTimers.remove(timer);
      }
    } catch (e) {
      debugPrint('🔴 TIMER: Error in periodic timer callback: $e');
      timer.cancel();
      _activeTimers.remove(timer);
    }
  });
  
  _activeTimers.add(timer);
  return timer;
}




// UI Event Handlers
void _onStartGamePressed() {
  if (!_gameStarted) {
    _startGame();
  }
}




void _onPlayerButtonPressed() {
  _showPlayerTable();
}




// Game Logic
void _startGame() async {
  final gameState = Provider.of<GameStateProvider>(context, listen: false);
   if (mounted) { setState(() {
    _gameStarted = true;
  }); }
   // Start timer
  gameState.startTimer();
   // Play countdown
  _playCountdown();
}




void _playCountdown() async {
  // Prevent multiple countdowns from running simultaneously
  if (_roundInProgress) {
    debugPrint('🔄 GAME: Round already in progress, skipping countdown');
    return;
  }

  // Cancel any existing timers
  _countdownTimer?.cancel();
  _resultTimer?.cancel();
  _nextRoundTimer?.cancel();

  if (mounted) { setState(() {
    _roundInProgress = true;
    _showingCountdown = true;
    _currentCountdownIndex = 0;
    _showingResult = false;
    _currentResultImage = null;
    _currentChoiceImage = null;
  }); }

  // IMPORTANT: Démarrer la caméra EN ARRIÈRE-PLAN avant le countdown
  // pour qu'elle soit prête à détecter le geste du joueur
  debugPrint('🎮 GAME: Starting camera in background before countdown');
  await _startGestureDetection();

  // Play countdown sound ONLY for the first round
  final gameState = Provider.of<GameStateProvider>(context, listen: false);
  if (_isFirstRound) {
    debugPrint('🎮 GAME: First round - playing enhanced countdown sequence');

    // Example of advanced audio: Play multiple sounds with timing
    await gameState.playEffectSequence([
      (file: 'audio/3_2_1_kids.mp3', delay: Duration.zero),
      // Could add more sounds here with different delays
    ]);

    _isFirstRound = false; // Mark that first round is done
  } else {
    debugPrint('🎮 GAME: Subsequent round - no countdown sound');
  }

  _showCountdownSequence();
}




void _showCountdownSequence() {
  if (_isDisposing || _isDisposed) return;
  
  if (_currentCountdownIndex < _countdownImages.length) {
    if (mounted) { setState(() {
      // Current countdown image will be displayed by the UI
    }); }

    // Schedule next countdown image using tracked timer
    _countdownTimer = _createTrackedTimer(const Duration(seconds: 1), () {
      if (mounted && _roundInProgress) {
        if (mounted) { setState(() {
          _currentCountdownIndex++;
        }); }
        _showCountdownSequence();
      }
    });
  } else {
    // Countdown finished, la caméra est déjà active en arrière-plan
    if (mounted) { setState(() {
      _showingCountdown = false;
    }); }
    debugPrint('🎮 GAME: Countdown finished, camera already detecting gestures');
  }
}












Future<void> _startGestureDetection() async {
  debugPrint('🎮 GAME: Starting gesture detection in GAME mode');




  try {
    // Vérifier d'abord si le détecteur est initialisé
    if (!gestureDetector.isInitialized) {
      debugPrint('🎮 GAME: Detector not initialized, initializing...');
      final initialized = await gestureDetector.initialize();
      if (!initialized) {
        debugPrint('🔴 GAME: Failed to initialize detector');
        _showErrorSnackBar('Impossible d\'initialiser la détection de gestes');
        // Skip this round without affecting game state
        _skipRoundDueToError();
        return;
      }
    }
  
    // Start detection in GAME mode for faster response with front camera
    await gestureDetector.startDetection(mode: "GAME", useFrontCamera: true);
    if (mounted) { setState(() {
      _isDetecting = true;
      _detectedGesture = null;
    }); }




    // Always set up gesture listener, regardless of whether we have a stream
    // The listener will handle both native stream and fallback cases
    _setupGestureListener();




    debugPrint('🎮 GAME: Gesture detection started successfully');

    // Timeout intelligent : 6 secondes pour le premier round (pour laisser le temps au son de finir)
    // 3 secondes pour les rounds suivants
    int timeoutSeconds = _isFirstRound ? 6 : 3;
    debugPrint('🎮 GAME: Setting timeout to $timeoutSeconds seconds (first round: $_isFirstRound)');

    _createTrackedTimer(Duration(seconds: timeoutSeconds), () {
      if (mounted && _isDetecting && _detectedGesture == null) {
        debugPrint('⏰ GAME: No gesture detected after $timeoutSeconds seconds, showing Round Cancelled');
        _skipRoundDueToError();
      }
    });
  } catch (e) {
    debugPrint('🔴 GAME: Error starting gesture detection: $e');
    // Skip this round without affecting game state
    _showErrorSnackBar('Erreur de détection de gestes');
    _skipRoundDueToError();
  }
}












































// Détection des gestes en temps réel
void _detectGesture() async {
  if (!_isDetecting) return;
   String playerGesture = gestureDetector.getGesture();
  debugPrint('🎮 GAME REAL: Checking for real gesture: "$playerGesture"');
   if (!['papier', 'pierre', 'ciseaux'].contains(playerGesture)) {
    // Wait and try again based on difficulty
    final gameState = Provider.of<GameStateProvider>(context, listen: false);
    double delay = _getDetectionDelay(gameState.difficulty);
  
    debugPrint('🎮 GAME REAL: No valid gesture detected, retrying in ${delay}s');
  
    Future.delayed(Duration(milliseconds: (delay * 1000).toInt()), () {
      if (mounted && _isDetecting) {
        _detectGesture();
      }
    });
  } else {
    // Valid gesture detected
    debugPrint('🎮 GAME REAL: Valid real gesture detected: "$playerGesture"');
    _processValidGesture(playerGesture);
  }
}
 // Process a valid gesture
void _processValidGesture(String playerGesture) async {
  // Only process if we haven't already processed a gesture
  if (!_isDetecting) return;
   debugPrint('🎮 GAME: Processing valid gesture: $playerGesture');
   await gestureDetector.stopDetection();
  if (mounted) { setState(() {
    _isDetecting = false;
  }); }
  _displayProgramChoice(playerGesture);
}




double _getDetectionDelay(String difficulty) {
  switch (difficulty) {
    case 'hard':
      return 0.5;
    case 'medium':
      return 0.75;
    case 'light':
      return 1;
    default:
      return 0.75;
  }
}




void _displayProgramChoice(String playerChoice) {
  // Random program choice
  final programChoiceImage = _choiceImages[math.Random().nextInt(_choiceImages.length)];
   if (mounted) { setState(() {
    _currentChoiceImage = programChoiceImage;
  }); }
   // Show program choice for 1.5 seconds then evaluate
  Future.delayed(const Duration(milliseconds: 1500), () {
    if (mounted) {
      _evaluateResult(playerChoice, programChoiceImage);
    }
  });
}




void _evaluateResult(String playerChoice, String programChoiceImage) {
  final gameState = Provider.of<GameStateProvider>(context, listen: false);

   // Extract program choice name from image path
  String programChoice = programChoiceImage.split('.')[0];
   // DEBUG: Log the choices for debugging
  debugPrint('🎮 GAME EVALUATION:');
  debugPrint('🎮 Player choice: $playerChoice');
  debugPrint('🎮 Program choice image: $programChoiceImage');
  debugPrint('🎮 Program choice extracted: $programChoice');
   String resultImage = 'egal.png';
  String soundFile = 'equal.mp3';
  bool playerWon = false;
   if (playerChoice == programChoice) {
    // Tie
    resultImage = 'egal.png';
    soundFile = 'audio/equal.mp3';
    debugPrint('🟡 GAME RESULT: TIE (both chose $playerChoice)');
  } else if ((playerChoice == 'papier' && programChoice == 'pierre') ||
             (playerChoice == 'pierre' && programChoice == 'ciseaux') ||
             (playerChoice == 'ciseaux' && programChoice == 'papier')) {
    // Player wins
    resultImage = 'you_win.png';
    soundFile = 'audio/you_win.mp3';
    playerWon = true;
    debugPrint('🟢 GAME RESULT: PLAYER WINS! ($playerChoice beats $programChoice)');
    gameState.updateScore(playerWon: true);
    gameState.updateLives(playerLostLife: false); // Program loses a life
  } else {
    // Player loses
    resultImage = 'you_lose.png';
    soundFile = 'audio/you_lose.mp3';
    playerWon = false;
    debugPrint('🔴 GAME RESULT: PLAYER LOSES! ($programChoice beats $playerChoice)');
    gameState.updateScore(playerWon: false);
    gameState.updateLives(playerLostLife: true); // Player loses a life
  }
   // Additional debugging
  debugPrint('🎮 Result image: $resultImage');
  debugPrint('🎮 Sound file: $soundFile');
  debugPrint('🎮 Player score: ${gameState.scorePlayer}');
  debugPrint('🎮 Program score: ${gameState.scoreProgram}');
  debugPrint('🎮 Player lives: ${gameState.playerLives}');
  debugPrint('🎮 Program lives: ${gameState.programLives}');
   // Play result sound
  gameState.playEffect(soundFile);
   // Show result
  _showResult(resultImage);
   // Check game over condition
  if (gameState.playerLives <= 0 || gameState.programLives <= 0) {
    String finalResult = gameState.playerLives > 0 ? 'Victory' : 'Defeat';
    gameState.saveGameResult(
      finalResult: finalResult,
      playerPoints: gameState.scorePlayer,
      programPoints: gameState.scoreProgram,
      playerJackpot: gameState.playerJackpotPoints,
      programJackpot: gameState.programJackpotPoints,
    );

    // Use tracked timer for game over
    _nextRoundTimer = _createTrackedTimer(const Duration(milliseconds: 1500), () {
      if (mounted) {
        _displayGameOver();
      }
    });
  } else {
    // Continue to next round using tracked timer
    _nextRoundTimer = _createTrackedTimer(const Duration(milliseconds: 1500), () {
      if (mounted) {
        _playNextRound();
      }
    });
  }
}




void _showResult(String resultImage) {
  // Cancel any existing result timer
  _resultTimer?.cancel();
  _resultTimer = null;

  if (mounted) { setState(() {
    _showingResult = true;
    _currentResultImage = resultImage;
    _currentChoiceImage = null; // Clear choice image
  }); }

  debugPrint('🎮 GAME: Showing result: $resultImage');

  // Keep result visible longer - hide after 2.5 seconds using tracked timer
  // This ensures the result is visible during the next round transition
  _resultTimer = _createTrackedTimer(const Duration(milliseconds: 2500), () {
    if (mounted) {
      setState(() {
        _showingResult = false;
        _currentResultImage = null;
      });
      debugPrint('🎮 GAME: Result display finished');
      // Clear timer reference after use
      _resultTimer = null;
    }
  });
}




void _playNextRound() async {
  final gameState = Provider.of<GameStateProvider>(context, listen: false);

  // Cancel any existing next round timer
  _nextRoundTimer?.cancel();
  _nextRoundTimer = null;

  // Reset round state
  if (mounted) { setState(() {
    _roundInProgress = false;
  }); }

  debugPrint('🎮 GAME: Starting next round');

  // Play countdown sound for subsequent rounds
  await gameState.playEffect('3_2_go/${gameState.sineshifterSound}');

  // Restart detection after delay
  _nextRoundTimer = _createTrackedTimer(const Duration(seconds: 1), () {
    if (mounted && !_roundInProgress) {
      _startNextRoundDetection();
      // Clear timer reference after use
      _nextRoundTimer = null;
    }
  });
}

// Method for subsequent rounds - no countdown images, direct to gesture detection
void _startNextRoundDetection() async {
  // Prevent multiple rounds from running simultaneously
  if (_roundInProgress) {
    debugPrint('🔄 GAME: Round already in progress, skipping next round');
    return;
  }

  // Cancel any existing timers
  _countdownTimer?.cancel();
  _resultTimer?.cancel();
  _nextRoundTimer?.cancel();

  if (mounted) { setState(() {
    _roundInProgress = true;
    _showingCountdown = false; // No countdown images for subsequent rounds
    _showingResult = false;
    _currentResultImage = null;
    _currentChoiceImage = null;
  }); }

  // Start gesture detection directly for subsequent rounds
  debugPrint('🎮 GAME: Starting gesture detection for subsequent round');
  await _startGestureDetectionForSubsequentRound();
}

// Method for gesture detection in subsequent rounds (shorter timeout)
Future<void> _startGestureDetectionForSubsequentRound() async {
  debugPrint('🎮 GAME: Starting gesture detection for subsequent round (3s timeout)');

  try {
    // Vérifier d'abord si le détecteur est initialisé
    if (!gestureDetector.isInitialized) {
      debugPrint('🎮 GAME: Detector not initialized, initializing...');
      final initialized = await gestureDetector.initialize();
      if (!initialized) {
        debugPrint('🔴 GAME: Failed to initialize detector');
        _showErrorSnackBar('Impossible d\'initialiser la détection de gestes');
        _skipRoundDueToError();
        return;
      }
    }

    // Start detection in GAME mode for faster response with front camera
    await gestureDetector.startDetection(mode: "GAME", useFrontCamera: true);
    if (mounted) { setState(() {
      _isDetecting = true;
      _detectedGesture = null;
    }); }

    // Always set up gesture listener
    _setupGestureListener();

    debugPrint('🎮 GAME: Gesture detection started successfully for subsequent round');

    // Timeout de 3 secondes pour les rounds suivants
    _createTrackedTimer(const Duration(seconds: 3), () {
      if (mounted && _isDetecting && _detectedGesture == null) {
        debugPrint('⏰ GAME: No gesture detected after 3 seconds in subsequent round, showing Round Cancelled');
        _skipRoundDueToError();
      }
    });
  } catch (e) {
    debugPrint('🔴 GAME: Error starting gesture detection for subsequent round: $e');
    _showErrorSnackBar('Erreur de détection de gestes');
    _skipRoundDueToError();
  }
}

// Method to skip round due to error without affecting game state
void _skipRoundDueToError() async {
  debugPrint('⚠️ GAME: Skipping round due to error - no game state changes');

  // Show round cancelled message with sheriff and whistle
  _showRoundCancelledMessage();
}

// Public method to trigger round cancellation from external code
void showTechnicalError() {
  debugPrint('🚨 GAME: Round cancellation triggered externally');
  _showRoundCancelledMessage();
}

// Show animated "Round Cancelled" message with sheriff and whistle
void _showRoundCancelledMessage() {
  // Cancel any existing timers
  _cancelledMessageTimer?.cancel();
  _countdownTimer?.cancel();
  _resultTimer?.cancel();
  _nextRoundTimer?.cancel();

  // IMPORTANT: Stop gesture detection completely to avoid conflicts
  gestureDetector.stopDetection();
  debugPrint('🛑 GAME: Stopped gesture detection for Round Cancelled');

  // Play whistle sound for round cancellation
  final gameState = Provider.of<GameStateProvider>(context, listen: false);
  gameState.playTechnicalErrorSound();

  // Reset round state and show round cancelled message with sheriff
  if (mounted) { setState(() {
    _roundInProgress = false;
    _isDetecting = false;
    _showingCountdown = false;
    _showingResult = true; // Show as result to display sheriff large
    _showingCancelledMessage = true;
    _currentResultImage = 'sheriff.png'; // Show sheriff image BIG like other game images
    _currentChoiceImage = null;
    _detectedGesture = null; // Clear any detected gesture
  }); }

  debugPrint('🚨 GAME: Showing "Round Cancelled" message with sheriff and whistle');

  // Hide round cancelled message after 4 seconds and restart
  _cancelledMessageTimer = _createTrackedTimer(const Duration(seconds: 4), () {
    if (mounted) {
      setState(() {
        _showingCancelledMessage = false;
        _currentResultImage = null; // Clear sheriff image
      });
      // Clear timer reference after use
      _cancelledMessageTimer = null;

      // Wait a bit more then restart next round (includes countdown sound)
      _nextRoundTimer = _createTrackedTimer(const Duration(milliseconds: 1000), () {
        if (mounted && !_roundInProgress) {
          debugPrint('🔄 GAME: Restarting after round cancellation');
          _playNextRound(); // This already includes countdown sound!
          // Clear timer reference after use
          _nextRoundTimer = null;
        }
      });
    }
  });
}






// Fonction corrigée
void _displayGameOver() {
  if (_gameOverOpen || !mounted) return;
  _gameOverOpen = true;

  debugPrint('🎮 GAME: Displaying game over dialog');

  final gameState = Provider.of<GameStateProvider>(context, listen: false);
  gameState.stopTimer();

  // Ensure all timers are cancelled before showing dialog
  _countdownTimer?.cancel();
  _resultTimer?.cancel();
  _nextRoundTimer?.cancel();
  _cancelledMessageTimer?.cancel();

  // Stop gesture detection (guarded)
  try {
    gestureDetector.stopDetection();
  } catch (e) {
    debugPrint('🟠 GAME: gestureDetector.stopDetection() error: $e');
  }

  // Reset all UI states
  if (mounted) {
    setState(() {
      _roundInProgress = false;
      _isDetecting = false;
      _showingCountdown = false;
      _showingResult = false;
      _showingCancelledMessage = false;
      _currentResultImage = null;
      _currentChoiceImage = null;
    });
  }

  // Play game over sound with proper error handling
  try {
    gameState.playEffect('audio/gameover.mp3');
  } catch (e) {
    debugPrint('🔴 GAME: Error playing game over sound: $e');
  }

  // Pause image-cache cleanups while the dialog is open
  try { ImageCacheManager.instance.pause(); } catch (_) {}

  // Delay to ensure audio starts & UI is stable
  _createTrackedTimer(const Duration(milliseconds: 800), () async {
    if (!mounted) {
      try { ImageCacheManager.instance.resume(); } catch (_) {}
      _gameOverOpen = false;
      return;
    }

    // Precache critical dialog visuals
    try {
      await precacheImage(const AssetImage('assets/images/gameover.png'), context);
      await precacheImage(const AssetImage('assets/images/play.png'), context);
      await precacheImage(const AssetImage('assets/images/quit.png'), context);
    } catch (e) {
      debugPrint('🟠 IMAGE_CACHE: precache failed: $e');
    }

    try {
      await showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (_) => _GameOverDialog(
          onRestart: _restartGame,
          onQuit: _quitGame,
        ),
      );
    } catch (e) {
      debugPrint('🔴 GAME: Error showing game over dialog: $e');
      try { _restartGame(); } catch (_) {}
    } finally {
      try { ImageCacheManager.instance.resume(); } catch (_) {}
      _gameOverOpen = false;
    }
  });
}





void _restartGame() {
  debugPrint('🔄 GAME: Restart game requested');

  try {
    final gameState = Provider.of<GameStateProvider>(context, listen: false);

    // Stop all audio first to prevent conflicts
    gameState.stopAllAudio();

    // Cancel all timers first
    _countdownTimer?.cancel();
    _resultTimer?.cancel();
    _nextRoundTimer?.cancel();
    _cancelledMessageTimer?.cancel();

    // Stop camera detection
    gestureDetector.stopDetection();

    // Reset game state
    gameState.restartGame();

    // Reset UI state
    if (mounted) {
      setState(() {
        _gameStarted = false;
        _isDetecting = false;
        _showingCountdown = false;
        _showingResult = false;
        _currentResultImage = null;
        _currentChoiceImage = null;
        _currentCountdownIndex = 0;
        _detectedGesture = null;
        _roundInProgress = false;
        _isFirstRound = true;
        _showingCancelledMessage = false;
      });
    }

    // Close dialog safely with delay
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    });

    debugPrint('✅ GAME: Game restarted successfully');
  } catch (e) {
    debugPrint('🔴 GAME: Error during restart: $e');
    // Force close dialog if error occurs
    try {
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    } catch (popError) {
      debugPrint('🔴 GAME: Error closing dialog: $popError');
    }
  }
}




void _quitGame() {
  debugPrint('🚪 GAME: Quit game requested');

  try {
    // Cancel all timers
    _countdownTimer?.cancel();
    _resultTimer?.cancel();
    _nextRoundTimer?.cancel();
    _cancelledMessageTimer?.cancel();

    // Stop camera detection
    gestureDetector.stopDetection();

    // Stop all audio
    final gameState = Provider.of<GameStateProvider>(context, listen: false);
    gameState.stopAllAudio();

    // Close dialog first
    if (mounted && Navigator.canPop(context)) {
      Navigator.pop(context);
    }

    // Small delay to ensure UI is clean before closing app
    Future.delayed(const Duration(milliseconds: 100), () {
      SystemNavigator.pop();
    });

    debugPrint('✅ GAME: Game quit successfully');
  } catch (e) {
    debugPrint('🔴 GAME: Error during quit: $e');
    // Force close anyway
    SystemNavigator.pop();
  }
}




void _showPlayerTable() {
  showDialog(
    context: context,
    builder: (context) => const PlayerTableDialog(),
  );
}




void _showErrorSnackBar(String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Colors.red,
    ),
  );
}




@override
Widget build(BuildContext context) {
  return Consumer<GameStateProvider>(
    builder: (context, gameState, child) {
      return Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(gameState.bgImage),
            fit: BoxFit.cover,
          ),
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          body: SafeArea(
            child: Stack(
              children: [
                // Main game content
                _buildGameContent(gameState),
              
                // Overlay content (countdown, choices, results)
                _buildOverlayContent(),
              
                // UI Controls
                _buildUIControls(gameState),
              ],
            ),
          ),
        ),
      );
    },
  );
}




Widget _buildGameContent(GameStateProvider gameState) {
  return Column(
    children: [
      // Top section: Timer, Lives, Score, Jackpot
      _buildTopSection(gameState),
    
      // Middle section: Main game area
      const Expanded(child: SizedBox()),
    
      // Bottom section: Player info and controls
      _buildBottomSection(gameState),
    ],
  );
}




Widget _buildTopSection(GameStateProvider gameState) {
  return Padding(
    padding: const EdgeInsets.all(16.0),
    child: Column(
      children: [
        // Timer at top center
        GameTimer(),
      
        const SizedBox(height: 10),
      
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Lives on left
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LifeIconsRow(
                  lives: gameState.playerLives,
                  isPlayer: true,
                ),
                const SizedBox(height: 5),
                LifeIconsRow(
                  lives: gameState.programLives,
                  isPlayer: false,
                ),
              ],
            ),
          
            // Score and Jackpot on right
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                ScoreDisplay(),
                const SizedBox(height: 5),
                JackpotDisplay(),
              ],
            ),
          ],
        ),
      ],
    ),
  );
}




Widget _buildBottomSection(GameStateProvider gameState) {
  return Padding(
    padding: const EdgeInsets.all(16.0),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Settings button
        IconButton(
          onPressed: widget.onSettingsPressed,
          icon: Image.asset(
            'assets/icons/settings-icon.png',
            width: 30,
            height: 30,
          ),
        ),
      
        // Player info
        PlayerInfo(),
      
        // Player button
        IconButton(
          onPressed: _onPlayerButtonPressed,
          icon: Image.asset(
            'assets/icons/player.png',
            width: 30,
            height: 30,
          ),
        ),
      ],
    ),
  );
}




Widget _buildOverlayContent() {
  return Center(
    child: AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: _buildCurrentOverlay(),
    ),
  );
}




Widget _buildCurrentOverlay() {
  if (!_gameStarted) {
    return GestureDetector(
      onTap: _onStartGamePressed,
      child: Image.asset(
        'assets/images/start_1.png',
        width: MediaQuery.of(context).size.width * 0.6,
        height: MediaQuery.of(context).size.width * 0.6,
        key: const ValueKey('start_button'),
      ),
    );
  }

  // Show "Round Cancelled" message with sheriff and whistle
  if (_showingCancelledMessage) {
    return _buildRoundCancelledDisplay();
  }

  if (_showingCountdown && _currentCountdownIndex < _countdownImages.length) {
    return Image.asset(
      'assets/images/${_countdownImages[_currentCountdownIndex]}',
      width: MediaQuery.of(context).size.width * 0.6,
      height: MediaQuery.of(context).size.width * 0.6,
      key: ValueKey('countdown_${_currentCountdownIndex}_${DateTime.now().millisecondsSinceEpoch ~/ 1000}'),
      errorBuilder: (context, error, stackTrace) {
        debugPrint('🔴 IMAGE ERROR: Failed to load countdown image: $error');
        return Container(
          width: MediaQuery.of(context).size.width * 0.6,
          height: MediaQuery.of(context).size.width * 0.6,
          color: Colors.grey.withOpacity(0.3),
          child: const Icon(Icons.timer, size: 60, color: Colors.white),
        );
      },
    );
  }

  if (_currentChoiceImage != null) {
    return Image.asset(
      'assets/images/$_currentChoiceImage',
      width: MediaQuery.of(context).size.width * 0.6,
      height: MediaQuery.of(context).size.width * 0.6,
      key: ValueKey('choice_${_currentChoiceImage}_${DateTime.now().millisecondsSinceEpoch ~/ 1000}'),
      errorBuilder: (context, error, stackTrace) {
        debugPrint('🔴 IMAGE ERROR: Failed to load choice image: $error');
        return Container(
          width: MediaQuery.of(context).size.width * 0.6,
          height: MediaQuery.of(context).size.width * 0.6,
          color: Colors.grey.withOpacity(0.3),
          child: const Icon(Icons.sports_esports, size: 60, color: Colors.white),
        );
      },
    );
  }

  if (_showingResult && _currentResultImage != null && !_showingCancelledMessage) {
    return Image.asset(
      'assets/images/$_currentResultImage',
      width: MediaQuery.of(context).size.width * 0.6,
      height: MediaQuery.of(context).size.width * 0.6,
      key: ValueKey('result_${_currentResultImage}_${DateTime.now().millisecondsSinceEpoch ~/ 1000}'),
      errorBuilder: (context, error, stackTrace) {
        debugPrint('🔴 IMAGE ERROR: Failed to load result image: $error');
        return Container(
          width: MediaQuery.of(context).size.width * 0.6,
          height: MediaQuery.of(context).size.width * 0.6,
          color: Colors.grey.withOpacity(0.3),
          child: const Icon(Icons.emoji_events, size: 60, color: Colors.white),
        );
      },
    );
  }

  if (_isDetecting) {
    if (_cameraError) {
      return _buildCameraErrorDisplay();
    }
    // Pas d'indicateur - libérer l'espace pour la réponse de l'ordinateur
    return const SizedBox.shrink();
  }

  return const SizedBox.shrink();
}




Widget _buildCameraPreview() {
  final cameraController = gestureDetector.cameraController;




  return Stack(
    children: [
      // Camera preview ou indicateur de chargement
      Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.width * 0.8,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.white, width: 3),
          color: cameraController?.value.isInitialized == true ? null : Colors.black54,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(17),
          child: cameraController?.value.isInitialized == true
              ? CameraPreview(cameraController!)
              : const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                ),
        ),
      ),




      // Informations de détection en overlay
      Positioned(
        top: 8,
        left: 8,
        right: 8,
        child: _buildDetectionInfo(),
      ),
    ],
  );
}




Widget _buildDetectionIndicator() {
  return Container(
    width: MediaQuery.of(context).size.width * 0.6,
    height: MediaQuery.of(context).size.width * 0.6,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(20),
      border: Border.all(color: Colors.white, width: 3),
      color: Colors.black.withOpacity(0.3),
    ),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.camera_alt,
          size: 48,
          color: Colors.white.withOpacity(0.8),
        ),
        const SizedBox(height: 16),
        Text(
          'Caméra active\nPrêt à détecter',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 16),
        _buildDetectionInfo(),
      ],
    ),
  );
}




Widget _buildDetectionInfo() {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    decoration: BoxDecoration(
      color: Colors.black.withOpacity(0.7),
      borderRadius: BorderRadius.circular(8),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Icon(
              gestureDetector.detectedHand == "Left" ? Icons.back_hand :
              gestureDetector.detectedHand == "Right" ? Icons.front_hand :
              Icons.help_outline,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              'Main: ${gestureDetector.detectedHand}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontFamily: 'Genos',
              ),
            ),
          ],
        ),
        if (gestureDetector.currentGesture != gesture_service.GestureType.none) ...[
          const SizedBox(height: 2),
          Text(
            'Geste: ${gestureDetector.currentGesture.name} (${(gestureDetector.currentConfidence * 100).toStringAsFixed(1)}%)',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontFamily: 'Genos',
            ),
          ),
        ],
      ],
    ),
  );
}




Widget _buildCameraErrorDisplay() {
  return Container(
    width: MediaQuery.of(context).size.width * 0.8,
    height: MediaQuery.of(context).size.width * 0.8,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(20),
      border: Border.all(color: Colors.red, width: 3),
      color: Colors.black54,
    ),
    child: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 50,
          ),
          const SizedBox(height: 10),
          const Text(
            'Camera Error',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            _cameraErrorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              // Try to reinitialize camera
              _initializeGestureDetector();
            },
            child: const Text('Retry Camera'),
          ),
        ],
      ),
    ),
  );
}




Widget _buildRoundCancelledDisplay() {
  return AnimatedBuilder(
    animation: _scaleController,
    builder: (context, child) {
      // Start the animation when this widget is built
      _scaleController.reset();
      _scaleController.forward();

      return Transform.scale(
        scale: Tween<double>(begin: 0.5, end: 1.0).animate(
          CurvedAnimation(
            parent: _scaleController,
            curve: Curves.elasticOut,
          ),
        ).value,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Sheriff image en haut, grand et visible
            if (_currentResultImage == 'sheriff.png')
              Container(
                margin: const EdgeInsets.only(bottom: 20),
                child: Image.asset(
                  'assets/images/$_currentResultImage',
                  width: MediaQuery.of(context).size.width * 0.80,
                  height: MediaQuery.of(context).size.width * 0.80,
                  key: ValueKey('sheriff_$_currentResultImage'),
                ),
              ),

            // Message container en dessous
            Container(
              width: MediaQuery.of(context).size.width * 0.85,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.red.withOpacity(0.9),
                    Colors.orange.withOpacity(0.9),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.white, width: 3),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.4),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Warning icon with pulse animation
                  AnimatedBuilder(
                    animation: _fadeController,
                    builder: (context, child) {
                      _fadeController.repeat(reverse: true);
                      return Opacity(
                        opacity: Tween<double>(begin: 0.5, end: 1.0).animate(_fadeController).value,
                        child: const Icon(
                          Icons.warning_rounded,
                          color: Colors.white,
                          size: 50,
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 15),

                  // "Round Cancelled" text (keeping original title)
                  const Text(
                    'ROUND CANCELLED',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Genos',
                      letterSpacing: 2,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 12),

                  // Error message in English as requested
                  const Text(
                    'This is a fault either from You,\nthe AI, or the System',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: 'Genos',
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 8),

                  // Restarting message
                  const Text(
                    'Restarting round...',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                      fontFamily: 'Genos',
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}

Widget _buildUIControls(GameStateProvider gameState) {
  return const SizedBox.shrink(); // Placeholder for additional controls if needed
}
}




// Game Over Dialog Widget
class _GameOverDialog extends StatefulWidget {
final VoidCallback onRestart;
final VoidCallback onQuit;

const _GameOverDialog({
  required this.onRestart,
  required this.onQuit,
});

@override
State<_GameOverDialog> createState() => _GameOverDialogState();
}

class _GameOverDialogState extends State<_GameOverDialog> {
bool _isProcessing = false;

@override
Widget build(BuildContext context) {
  return WillPopScope(
    onWillPop: () async => false, // Prevent back button from closing dialog
    child: Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          image: const DecorationImage(
            image: AssetImage('assets/images/blurred_bg.png'),
            fit: BoxFit.cover,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Game Over Image
            Image.asset(
              'assets/images/gameover.png',
              width: 200,
              height: 150,
            ),

            const SizedBox(height: 20),

            // Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Quit Button
                GestureDetector(
                  onTap: _isProcessing ? null : () async {
                    if (!_isProcessing) {
                      // TODO(Soraya): Guard ajouté manuellement pour setState dans expression fléchée
                      if (!mounted) return;
                      setState(() => _isProcessing = true);
                      debugPrint('🚪 DIALOG: Quit button pressed');

                      // Add small delay to show button press feedback
                      await Future.delayed(const Duration(milliseconds: 200));

                      try {
                        widget.onQuit();
                      } catch (e) {
                        debugPrint('🔴 DIALOG: Error in quit callback: $e');
                        // TODO(Soraya): Guard ajouté manuellement pour setState dans expression fléchée
                        if (!mounted) return;
                        setState(() => _isProcessing = false);
                      }
                    }
                  },
                  child: Opacity(
                    opacity: _isProcessing ? 0.5 : 1.0,
                    child: Image.asset(
                      'assets/images/quit.png',
                      width: 80,
                      height: 80,
                    ),
                  ),
                ),

                // Play Again Button
                GestureDetector(
                  onTap: _isProcessing ? null : () async {
                    if (!_isProcessing) {
                      // TODO(Soraya): Guard ajouté manuellement pour setState dans expression fléchée
                      if (!mounted) return;
                      setState(() => _isProcessing = true);
                      debugPrint('🔄 DIALOG: Restart button pressed');

                      // Add small delay to show button press feedback
                      await Future.delayed(const Duration(milliseconds: 200));

                      try {
                        widget.onRestart();
                      } catch (e) {
                        debugPrint('🔴 DIALOG: Error in restart callback: $e');
                        // TODO(Soraya): Guard ajouté manuellement pour setState dans expression fléchée
                        if (!mounted) return;
                        setState(() => _isProcessing = false);
                      }
                    }
                  },
                  child: Opacity(
                    opacity: _isProcessing ? 0.5 : 1.0,
                    child: Image.asset(
                      'assets/images/play.png',
                      width: 80,
                      height: 80,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );
}
}











