import 'package:flutter/material.dart';
import '../services/image_cache_manager.dart';

/// Optimized image widget for game results with memory management and error recovery
class OptimizedGameImage extends StatefulWidget {
  final String imagePath;
  final double width;
  final double height;
  final String semanticLabel;
  final IconData fallbackIcon;
  
  const OptimizedGameImage({
    super.key,
    required this.imagePath,
    required this.width,
    required this.height,
    required this.semanticLabel,
    this.fallbackIcon = Icons.image,
  });

  @override
  State<OptimizedGameImage> createState() => _OptimizedGameImageState();
}

class _OptimizedGameImageState extends State<OptimizedGameImage> {
  bool _imageLoadFailed = false;
  int _retryCount = 0;
  static const int _maxRetries = 2;
  
  @override
  Widget build(BuildContext context) {
    // If image loading failed after retries, show fallback
    if (_imageLoadFailed && _retryCount >= _maxRetries) {
      return _buildFallbackWidget();
    }
    
    return Image.asset(
      widget.imagePath,
      width: widget.width,
      height: widget.height,
      semanticLabel: widget.semanticLabel,
      // Use a simple key that doesn't change unnecessarily
      key: ValueKey('game_image_${widget.imagePath.split('/').last}'),
      errorBuilder: (context, error, stackTrace) {
        debugPrint('🔴 OPTIMIZED_IMAGE: Failed to load ${widget.imagePath}: $error');
        _handleImageError();
        return _buildFallbackWidget();
      },
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        // Reset error state if image loads successfully
        if (frame != null && _imageLoadFailed) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _imageLoadFailed = false;
                _retryCount = 0;
              });
            }
          });
        }
        return child;
      },
    );
  }
  
  void _handleImageError() {
    if (!mounted) return;
    
    setState(() {
      _imageLoadFailed = true;
      _retryCount++;
    });
    
    // Try to clear cache and reload if under retry limit
    if (_retryCount < _maxRetries) {
      debugPrint('🔄 OPTIMIZED_IMAGE: Attempting retry ${_retryCount + 1} for ${widget.imagePath}');
      
      // Clear specific image from cache
      ImageCacheManager.instance.clearSpecificImages([widget.imagePath]);
      
      // Retry after a short delay
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          setState(() {
            _imageLoadFailed = false;
          });
        }
      });
    } else {
      debugPrint('⚠️ OPTIMIZED_IMAGE: Max retries reached for ${widget.imagePath}, showing fallback');
    }
  }
  
  Widget _buildFallbackWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.3),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.white.withOpacity(0.5), width: 2),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            widget.fallbackIcon,
            size: widget.width * 0.3,
            color: Colors.white.withOpacity(0.8),
          ),
          const SizedBox(height: 8),
          Text(
            widget.semanticLabel,
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}