import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpSupportScreen extends StatefulWidget {
  const HelpSupportScreen({super.key});

  @override
  State<HelpSupportScreen> createState() => _HelpSupportScreenState();
}

class _HelpSupportScreenState extends State<HelpSupportScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Help & Support',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1E3A8A), // Deep blue
              Color(0xFF3B82F6), // Blue
              Color(0xFF8B5CF6), // Purple
            ],
          ),
        ),
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeSection(),
              const SizedBox(height: 30),
              _buildGameBasicsSection(),
              const SizedBox(height: 30),
              _buildGestureGuideSection(),
              const SizedBox(height: 30),
              _buildScoringSystemSection(),
              const SizedBox(height: 30),
              _buildJackpotSystemSection(),
              const SizedBox(height: 30),
              _buildDifficultySection(),
              const SizedBox(height: 30),
              _buildTeamPlaySection(),
              const SizedBox(height: 30),
              _buildTipsSection(),
              const SizedBox(height: 30),
              _buildSupportSection(),
              const SizedBox(height: 50),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return _buildSection(
      icon: Icons.waving_hand,
      title: 'Welcome to Rock Paper Scissors!',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildText(
            'Experience the classic game like never before with AI-powered hand gesture recognition! '
            'Use your camera to play with real hand gestures instead of tapping buttons.',
          ),
          const SizedBox(height: 15),
          _buildText(
            'This revolutionary approach makes the game more immersive, fun, and challenging. '
            'Perfect for players of all ages!',
          ),
        ],
      ),
    );
  }

  Widget _buildGameBasicsSection() {
    return _buildSection(
      icon: Icons.sports_esports,
      title: 'Game Basics',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBulletPoint('🎯', 'Objective: Beat the AI opponent by winning rounds'),
          _buildBulletPoint('❤️', 'Lives: Each player starts with 3 lives (customizable in settings)'),
          _buildBulletPoint('🏆', 'Victory: Reduce opponent\'s lives to 0 to win the game'),
          _buildBulletPoint('⏱️', 'Timer: Track your game duration and improve your speed'),
          _buildBulletPoint('🎮', 'Controls: Use hand gestures in front of your camera'),
        ],
      ),
    );
  }

  Widget _buildGestureGuideSection() {
    return _buildSection(
      icon: Icons.pan_tool,
      title: 'Hand Gesture Guide',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildText('Master these three gestures to play:', isBold: true),
          const SizedBox(height: 15),
          _buildGestureItem('✊', 'ROCK (Pierre)', 'Make a closed fist', 'Beats Scissors, loses to Paper'),
          _buildGestureItem('✋', 'PAPER (Papier)', 'Open palm facing camera', 'Beats Rock, loses to Scissors'),
          _buildGestureItem('✌️', 'SCISSORS (Ciseaux)', 'Peace sign (V shape)', 'Beats Paper, loses to Rock'),
          const SizedBox(height: 15),
          _buildText('💡 Tips for better detection:', isBold: true),
          _buildBulletPoint('📱', 'Hold your hand steady for 2-3 seconds'),
          _buildBulletPoint('💡', 'Ensure good lighting'),
          _buildBulletPoint('📏', 'Keep your hand at arm\'s length from camera'),
          _buildBulletPoint('🎯', 'Center your hand in the camera view'),
        ],
      ),
    );
  }

  Widget _buildScoringSystemSection() {
    return _buildSection(
      icon: Icons.emoji_events,
      title: 'Scoring System',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildText('Understand how points and lives work:', isBold: true),
          const SizedBox(height: 15),
          _buildBulletPoint('🎯', 'Win a round: +1 point, opponent loses 1 life'),
          _buildBulletPoint('🟡', 'Tie: No points awarded, no lives lost'),
          _buildBulletPoint('❌', 'Lose a round: Opponent gets +1 point, you lose 1 life'),
          _buildBulletPoint('🏁', 'Game ends when any player reaches 0 lives'),
          _buildBulletPoint('📊', 'Your final score and game time are saved'),
        ],
      ),
    );
  }

  Widget _buildJackpotSystemSection() {
    return _buildSection(
      icon: Icons.casino,
      title: 'Jackpot & Bonus System',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildText('Earn bonus points and extra lives:', isBold: true),
          const SizedBox(height: 15),
          _buildText('🎰 Jackpot Points:', isBold: true),
          _buildBulletPoint('🥇', 'First consecutive win: +100 jackpot points'),
          _buildBulletPoint('🔥', '2+ consecutive wins: +2500 jackpot points each!'),
          _buildBulletPoint('🎉', 'Bonus sound effects play for big jackpot wins'),
          const SizedBox(height: 15),
          _buildText('💎 Extra Life System:', isBold: true),
          _buildBulletPoint('⚡', 'When you have 3000+ jackpot points AND only 1 life left'),
          _buildBulletPoint('🛡️', 'Automatically spend 3000 points to buy an extra life'),
          _buildBulletPoint('🔄', 'This gives you a second chance to continue playing'),
          _buildBulletPoint('🎵', 'Special jackpot sound plays when buying extra life'),
        ],
      ),
    );
  }

  Widget _buildDifficultySection() {
    return _buildSection(
      icon: Icons.speed,
      title: 'Difficulty Levels',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildText('Choose your challenge level:', isBold: true),
          const SizedBox(height: 15),
          _buildDifficultyItem('🟢', 'LIGHT', 'Relaxed pace, 1.0s detection delay'),
          _buildDifficultyItem('🟡', 'MEDIUM', 'Balanced gameplay, 0.5s detection delay'),
          _buildDifficultyItem('🔴', 'HARD', 'Fast-paced action, instant detection'),
          const SizedBox(height: 15),
          _buildText('💡 Detection delay affects how quickly the game responds to your gestures.'),
        ],
      ),
    );
  }

  Widget _buildTeamPlaySection() {
    return _buildSection(
      icon: Icons.groups,
      title: 'Team Play & Multiplayer',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildText('Play with friends and family:', isBold: true),
          const SizedBox(height: 15),
          _buildBulletPoint('👥', 'Multiple players can take turns using the same device'),
          _buildBulletPoint('📝', 'Player names are saved and tracked individually'),
          _buildBulletPoint('🏆', 'Each player\'s game history is stored separately'),
          _buildBulletPoint('📊', 'View player statistics and compare performances'),
          _buildBulletPoint('🎮', 'Pass the device between players for tournament-style play'),
          const SizedBox(height: 15),
          _buildText('🎯 Tournament Tips:', isBold: true),
          _buildBulletPoint('⏱️', 'Set time limits for each player\'s turn'),
          _buildBulletPoint('🏅', 'Track who has the highest score'),
          _buildBulletPoint('🎪', 'Use different difficulty levels for different skill levels'),
        ],
      ),
    );
  }

  Widget _buildTipsSection() {
    return _buildSection(
      icon: Icons.lightbulb,
      title: 'Pro Tips & Strategies',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildText('🎯 Gesture Detection Tips:', isBold: true),
          _buildBulletPoint('📱', 'Use front camera for better hand visibility'),
          _buildBulletPoint('🌟', 'Practice in Training mode before playing'),
          _buildBulletPoint('🎭', 'Make clear, distinct gestures'),
          _buildBulletPoint('⏰', 'Hold gestures steady for 2-3 seconds'),
          const SizedBox(height: 15),
          _buildText('🧠 Strategy Tips:', isBold: true),
          _buildBulletPoint('🔥', 'Go for consecutive wins to maximize jackpot points'),
          _buildBulletPoint('💎', 'Save jackpot points for when you have 1 life left'),
          _buildBulletPoint('🎲', 'Vary your gestures to be unpredictable'),
          _buildBulletPoint('⚡', 'Use higher difficulty for more intense gameplay'),
        ],
      ),
    );
  }

  Widget _buildSupportSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.support_agent,
            size: 50,
            color: Colors.white,
          ),
          const SizedBox(height: 15),
          const Text(
            'Need Help?',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Contact our support team for technical issues, feedback, or suggestions.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: _contactSupport,
            icon: const Icon(Icons.email),
            label: const Text('Contact Support'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: const Color(0xFF1E3A8A),
              padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            '<EMAIL>',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white60,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDifficultyItem(String emoji, String level, String description) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  level,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _contactSupport() async {
    const email = '<EMAIL>';
    const subject = 'Rock Paper Scissors - Support Request';
    const body = 'Hello,\n\nI need help with the Rock Paper Scissors app.\n\nIssue description:\n\n\nDevice information:\n- Device model: \n- Android version: \n\nThank you!';

    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        // Fallback: copy email to clipboard
        await Clipboard.setData(const ClipboardData(text: email));
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Email address copied to clipboard: <EMAIL>'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error launching email: $e');
      // Fallback: copy email to clipboard
      await Clipboard.setData(const ClipboardData(text: email));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Email address copied to clipboard: <EMAIL>'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  // Helper methods
  Widget _buildSection({
    required IconData icon,
    required String title,
    required Widget content,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          content,
        ],
      ),
    );
  }

  Widget _buildText(String text, {bool isBold = false}) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 16,
        color: Colors.white,
        fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
        height: 1.4,
      ),
    );
  }

  Widget _buildBulletPoint(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            emoji,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGestureItem(String emoji, String name, String description, String strategy) {
    return Container(
      margin: const EdgeInsets.only(bottom: 15),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(emoji, style: const TextStyle(fontSize: 24)),
              const SizedBox(width: 10),
              Text(
                name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 5),
          Text(
            strategy,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white70,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }
}