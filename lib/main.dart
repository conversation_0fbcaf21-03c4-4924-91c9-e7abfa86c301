import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'screens/welcome_screen.dart';
import 'screens/main_screen.dart';
import 'screens/settings_screen.dart';
import 'providers/game_state_provider.dart';
import 'providers/settings_provider.dart';
import 'package:permission_handler/permission_handler.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Démarrer l'app d'abord, puis demander les permissions
  runApp(const RockPaperScissorsApp(hasCameraPermission: true));

  // Demander les permissions après un délai pour éviter le blocage
  Future.delayed(const Duration(milliseconds: 500), () async {
    try {
      final status = await Permission.camera.request();
      debugPrint('🎮 CAMERA: Permission status: $status');
      if (!status.isGranted) {
        debugPrint('🔴 CAMERA: Permission denied, camera access is required');
      }
    } catch (e) {
      debugPrint('🔴 CAMERA ERROR: Error requesting permission: $e');
    }
  });
}



class RockPaperScissorsApp extends StatelessWidget {
  final bool hasCameraPermission;

  const RockPaperScissorsApp({super.key, required this.hasCameraPermission});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => GameStateProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
      ],
      child: MaterialApp(
        title: 'Rock Paper Scissors',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          visualDensity: VisualDensity.adaptivePlatformDensity,
          fontFamily: 'Genos',
        ),
        home: const WelcomeScreen(),  // Toujours démarrer avec l'écran de bienvenue
        routes: {
          '/welcome': (context) => const WelcomeScreen(),
          '/main': (context) => const MainScreen(),
          '/settings': (context) => const SettingsScreen(),
        },
      ),
    );
  }
}

